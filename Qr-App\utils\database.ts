/**
 * Database utility untuk mengelola SQLite database
 * Menangani operasi CRUD untuk scan history dan pengaturan aplikasi
 */

import * as SQLite from 'expo-sqlite';
import { ScanResult, AppSettings, HistoryFilter, SortOptions } from '../types';

// Nama database
const DATABASE_NAME = 'qr_scanner.db';

/**
 * Kelas untuk mengelola database SQLite
 */
export class DatabaseManager {
  private db: SQLite.SQLiteDatabase | null = null;

  /**
   * Inisialisasi database dan buat tabel jika belum ada
   */
  async initialize(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync(DATABASE_NAME);
      await this.createTables();
      console.log('Database berhasil diinisialisasi');
    } catch (error) {
      console.error('Error inisialisasi database:', error);
      throw error;
    }
  }

  /**
   * Membuat tabel-tabel yang diperlukan
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    // Tabel untuk scan history
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS scan_history (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        type TEXT NOT NULL,
        format TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        scan_method TEXT NOT NULL,
        location_latitude REAL,
        location_longitude REAL,
        location_address TEXT,
        is_favorite INTEGER DEFAULT 0
      );
    `);

    // Tabel untuk pengaturan aplikasi
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      );
    `);

    // Index untuk performa yang lebih baik
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_timestamp ON scan_history(timestamp);
      CREATE INDEX IF NOT EXISTS idx_type ON scan_history(type);
      CREATE INDEX IF NOT EXISTS idx_favorite ON scan_history(is_favorite);
    `);
  }

  /**
   * Menyimpan hasil scan ke database
   */
  async saveScanResult(scanResult: ScanResult): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      await this.db.runAsync(
        `INSERT INTO scan_history 
         (id, data, type, format, timestamp, scan_method, location_latitude, location_longitude, location_address, is_favorite)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          scanResult.id,
          scanResult.data,
          scanResult.type,
          scanResult.format,
          scanResult.timestamp.getTime(),
          scanResult.scanMethod,
          scanResult.location?.latitude || null,
          scanResult.location?.longitude || null,
          scanResult.location?.address || null,
          scanResult.isFavorite ? 1 : 0
        ]
      );
      console.log('Scan result berhasil disimpan:', scanResult.id);
    } catch (error) {
      console.error('Error menyimpan scan result:', error);
      throw error;
    }
  }

  /**
   * Mengambil history scan dengan filter dan sorting
   */
  async getScanHistory(
    filter?: HistoryFilter,
    sort?: SortOptions,
    limit?: number,
    offset?: number
  ): Promise<ScanResult[]> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    let query = 'SELECT * FROM scan_history WHERE 1=1';
    const params: any[] = [];

    // Aplikasikan filter
    if (filter) {
      if (filter.type) {
        query += ' AND type = ?';
        params.push(filter.type);
      }
      if (filter.dateFrom) {
        query += ' AND timestamp >= ?';
        params.push(filter.dateFrom.getTime());
      }
      if (filter.dateTo) {
        query += ' AND timestamp <= ?';
        params.push(filter.dateTo.getTime());
      }
      if (filter.searchQuery) {
        query += ' AND data LIKE ?';
        params.push(`%${filter.searchQuery}%`);
      }
      if (filter.isFavorite !== undefined) {
        query += ' AND is_favorite = ?';
        params.push(filter.isFavorite ? 1 : 0);
      }
    }

    // Aplikasikan sorting
    if (sort) {
      query += ` ORDER BY ${sort.field} ${sort.direction.toUpperCase()}`;
    } else {
      query += ' ORDER BY timestamp DESC';
    }

    // Aplikasikan limit dan offset
    if (limit) {
      query += ' LIMIT ?';
      params.push(limit);
      if (offset) {
        query += ' OFFSET ?';
        params.push(offset);
      }
    }

    try {
      const result = await this.db.getAllAsync(query, params);
      return result.map(this.mapRowToScanResult);
    } catch (error) {
      console.error('Error mengambil scan history:', error);
      throw error;
    }
  }

  /**
   * Mengubah row database menjadi ScanResult object
   */
  private mapRowToScanResult(row: any): ScanResult {
    return {
      id: row.id,
      data: row.data,
      type: row.type,
      format: row.format,
      timestamp: new Date(row.timestamp),
      scanMethod: row.scan_method,
      location: row.location_latitude && row.location_longitude ? {
        latitude: row.location_latitude,
        longitude: row.location_longitude,
        address: row.location_address
      } : undefined,
      isFavorite: row.is_favorite === 1
    };
  }

  /**
   * Menghapus scan result berdasarkan ID
   */
  async deleteScanResult(id: string): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      await this.db.runAsync('DELETE FROM scan_history WHERE id = ?', [id]);
      console.log('Scan result berhasil dihapus:', id);
    } catch (error) {
      console.error('Error menghapus scan result:', error);
      throw error;
    }
  }

  /**
   * Menghapus multiple scan results
   */
  async deleteScanResults(ids: string[]): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      const placeholders = ids.map(() => '?').join(',');
      await this.db.runAsync(`DELETE FROM scan_history WHERE id IN (${placeholders})`, ids);
      console.log('Multiple scan results berhasil dihapus:', ids.length);
    } catch (error) {
      console.error('Error menghapus multiple scan results:', error);
      throw error;
    }
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(id: string): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      await this.db.runAsync(
        'UPDATE scan_history SET is_favorite = NOT is_favorite WHERE id = ?',
        [id]
      );
      console.log('Favorite status berhasil diubah:', id);
    } catch (error) {
      console.error('Error mengubah favorite status:', error);
      throw error;
    }
  }

  /**
   * Membersihkan history lama berdasarkan retention period
   */
  async cleanupOldHistory(retentionDays: number): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    try {
      await this.db.runAsync(
        'DELETE FROM scan_history WHERE timestamp < ? AND is_favorite = 0',
        [cutoffDate.getTime()]
      );
      console.log('History lama berhasil dibersihkan');
    } catch (error) {
      console.error('Error membersihkan history lama:', error);
      throw error;
    }
  }

  /**
   * Menyimpan pengaturan aplikasi
   */
  async saveSetting(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      await this.db.runAsync(
        'INSERT OR REPLACE INTO app_settings (key, value) VALUES (?, ?)',
        [key, JSON.stringify(value)]
      );
    } catch (error) {
      console.error('Error menyimpan pengaturan:', error);
      throw error;
    }
  }

  /**
   * Mengambil pengaturan aplikasi
   */
  async getSetting(key: string, defaultValue?: any): Promise<any> {
    if (!this.db) throw new Error('Database belum diinisialisasi');

    try {
      const result = await this.db.getFirstAsync(
        'SELECT value FROM app_settings WHERE key = ?',
        [key]
      );
      return result ? JSON.parse(result.value as string) : defaultValue;
    } catch (error) {
      console.error('Error mengambil pengaturan:', error);
      return defaultValue;
    }
  }

  /**
   * Menutup koneksi database
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.closeAsync();
      this.db = null;
      console.log('Database connection ditutup');
    }
  }
}

// Instance singleton database manager
export const databaseManager = new DatabaseManager();
