/**
 * Screen utama untuk QR Scanner
 * Menampilkan kamera scanner dan men<PERSON><PERSON> hasil scan
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  StatusBar,
  Alert,
  SafeAreaView
} from 'react-native';
import QRScanner from '@/components/QRScanner';
import ScanResult from '@/components/ScanResult';
import { ScanResult as ScanResultType } from '@/types';
import { databaseManager } from '@/utils/database';

export default function ScannerScreen() {
  const [currentScanResult, setCurrentScanResult] = useState<ScanResultType | null>(null);
  const [showResult, setShowResult] = useState(false);

  /**
   * Inisialisasi database saat komponen dimount
   */
  useEffect(() => {
    initializeDatabase();
  }, []);

  /**
   * Inisialisasi database
   */
  const initializeDatabase = async () => {
    try {
      await databaseManager.initialize();
      console.log('Database berhasil diinisialisasi');
    } catch (error) {
      console.error('Error inisialisasi database:', error);
      Alert.alert(
        'Error Database',
        'Gagal menginisialisasi database. Beberapa fitur mungkin tidak berfungsi.'
      );
    }
  };

  /**
   * Handle hasil scan berhasil
   */
  const handleScanSuccess = (scanResult: ScanResultType) => {
    setCurrentScanResult(scanResult);
    setShowResult(true);
  };

  /**
   * Handle error saat scanning
   */
  const handleScanError = (error: string) => {
    Alert.alert('Error Scanning', error);
  };

  /**
   * Tutup modal hasil scan
   */
  const closeScanResult = () => {
    setShowResult(false);
    setCurrentScanResult(null);
  };

  /**
   * Toggle favorite status
   */
  const toggleFavorite = async () => {
    if (!currentScanResult) return;

    try {
      await databaseManager.toggleFavorite(currentScanResult.id);
      setCurrentScanResult({
        ...currentScanResult,
        isFavorite: !currentScanResult.isFavorite
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Gagal mengubah status favorite');
    }
  };

  /**
   * Simpan hasil scan ke database (jika belum auto-save)
   */
  const saveScanResult = async () => {
    if (!currentScanResult) return;

    try {
      await databaseManager.saveScanResult(currentScanResult);
      Alert.alert('Berhasil', 'Hasil scan berhasil disimpan');
    } catch (error) {
      console.error('Error saving scan result:', error);
      Alert.alert('Error', 'Gagal menyimpan hasil scan');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* QR Scanner */}
      <QRScanner
        onScanSuccess={handleScanSuccess}
        onError={handleScanError}
        enableSound={true}
        enableVibration={true}
        autoSave={true}
      />

      {/* Modal untuk menampilkan hasil scan */}
      <Modal
        visible={showResult}
        animationType="slide"
        transparent={true}
        onRequestClose={closeScanResult}
      >
        {currentScanResult && (
          <ScanResult
            scanResult={currentScanResult}
            onClose={closeScanResult}
            onFavorite={toggleFavorite}
            onSave={saveScanResult}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
}

/**
 * Styles untuk ScannerScreen
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
});
