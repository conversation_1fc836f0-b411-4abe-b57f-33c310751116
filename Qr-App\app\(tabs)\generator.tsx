/**
 * Screen untuk QR Code Generator
 * Mengin<PERSON><PERSON><PERSON><PERSON> komponen QRGenerator dengan fitur tambahan
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert
} from 'react-native';
import QRGenerator from '@/components/QRGenerator';
import { databaseManager } from '@/utils/database';
import { ScanResult, ScanMethod, BarcodeFormat } from '@/types';
import { detectScanType, generateScanId } from '@/utils/qrUtils';

export default function GeneratorScreen() {
  /**
   * Handle saat QR code berhasil di-generate
   */
  const handleQRGenerated = async (data: string, type: string) => {
    try {
      // Simpan QR code yang di-generate ke history
      const scanResult: ScanResult = {
        id: generateScanId(),
        data: data,
        type: detectScanType(data),
        format: BarcodeFormat.QR_CODE,
        timestamp: new Date(),
        scanMethod: ScanMethod.CAMERA, // Marking as generated
        isFavorite: false
      };

      // Auto save ke database
      await databaseManager.saveScanResult(scanResult);
      console.log('Generated QR code saved to history:', scanResult.id);
      
    } catch (error) {
      console.error('Error saving generated QR code:', error);
      // Don't show error to user as this is not critical
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* QR Generator */}
      <QRGenerator onGenerated={handleQRGenerated} />
    </SafeAreaView>
  );
}

/**
 * Styles untuk GeneratorScreen
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
