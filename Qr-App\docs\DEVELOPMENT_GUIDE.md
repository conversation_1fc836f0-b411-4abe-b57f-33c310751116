# Panduan Pengembangan - QR Scanner Pro

Panduan lengkap untuk developer yang ingin berkontribusi atau mengembangkan aplikasi QR Scanner Pro.

## 📋 Daftar Isi

1. [Setup Development Environment](#setup-development-environment)
2. [Struktur Kode](#struktur-kode)
3. [Coding Standards](#coding-standards)
4. [Testing Guidelines](#testing-guidelines)
5. [Debugging](#debugging)
6. [Performance Optimization](#performance-optimization)
7. [Deployment](#deployment)

## 🛠️ Setup Development Environment

### Prerequisites
- **Node.js**: v16.0.0 atau lebih baru
- **npm**: v7.0.0 atau lebih baru (atau yarn v1.22.0+)
- **Expo CLI**: v6.0.0 atau lebih baru
- **Git**: untuk version control

### Android Development
- **Android Studio**: Latest stable version
- **Android SDK**: API Level 21+ (Android 5.0+)
- **Java Development Kit**: JDK 11 atau lebih baru

### iOS Development (macOS only)
- **Xcode**: Latest stable version
- **iOS Simulator**: iOS 13.0+
- **CocoaPods**: v1.10.0+

### Setup Steps

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd Qr-App
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file dengan konfigurasi yang sesuai
   ```

4. **Start development server**
   ```bash
   npm start
   ```

5. **Run pada device/emulator**
   ```bash
   # Android
   npm run android
   
   # iOS
   npm run ios
   
   # Web (untuk testing)
   npm run web
   ```

## 🏗️ Struktur Kode

### Folder Structure
```
Qr-App/
├── app/                    # Screen utama (Expo Router)
│   ├── (tabs)/            # Tab navigation
│   │   ├── index.tsx      # Scanner screen
│   │   ├── gallery.tsx    # Gallery scanner
│   │   ├── explore.tsx    # History screen
│   │   └── generator.tsx  # QR generator
│   └── _layout.tsx        # Root layout
├── components/            # Reusable components
├── types/                 # TypeScript definitions
├── utils/                 # Utility functions
├── assets/               # Static assets
├── __tests__/            # Test files
└── docs/                 # Documentation
```

### Component Architecture

#### 1. Screen Components (app/)
- Menggunakan Expo Router untuk navigation
- Mengelola state level aplikasi
- Mengintegrasikan multiple components
- Handle navigation dan deep linking

#### 2. Feature Components (components/)
- Komponen yang dapat digunakan kembali
- Fokus pada satu fitur spesifik
- Props interface yang jelas
- Dokumentasi JSDoc lengkap

#### 3. Utility Functions (utils/)
- Pure functions tanpa side effects
- Mudah di-test dan di-mock
- Dokumentasi parameter dan return value
- Error handling yang proper

### State Management
- **Local State**: React hooks (useState, useEffect)
- **Global State**: Context API untuk shared data
- **Persistent State**: SQLite database untuk data storage
- **Cache**: Memory cache untuk performance optimization

## 📝 Coding Standards

### TypeScript Guidelines

#### 1. Type Definitions
```typescript
// ✅ Good: Explicit interface definitions
interface ScanResult {
  id: string;
  data: string;
  type: ScanType;
  timestamp: Date;
}

// ❌ Bad: Using any type
const result: any = getScanResult();
```

#### 2. Function Signatures
```typescript
// ✅ Good: Clear parameter and return types
async function saveScanResult(
  result: ScanResult,
  options?: SaveOptions
): Promise<void> {
  // Implementation
}

// ❌ Bad: Missing type annotations
async function saveScanResult(result, options) {
  // Implementation
}
```

#### 3. Component Props
```typescript
// ✅ Good: Interface for props
interface QRScannerProps {
  onScanSuccess: (result: ScanResult) => void;
  onError?: (error: string) => void;
  enableSound?: boolean;
}

export default function QRScanner({
  onScanSuccess,
  onError,
  enableSound = true
}: QRScannerProps) {
  // Component implementation
}
```

### React Guidelines

#### 1. Component Structure
```typescript
/**
 * Komponen untuk scanning QR Code
 * Menangani camera access dan barcode detection
 */
export default function QRScanner(props: QRScannerProps) {
  // 1. Hooks
  const [isScanning, setIsScanning] = useState(true);
  
  // 2. Effects
  useEffect(() => {
    // Setup logic
  }, []);
  
  // 3. Event handlers
  const handleScanSuccess = useCallback((result: ScanResult) => {
    // Handle scan success
  }, []);
  
  // 4. Render helpers
  const renderScanOverlay = () => {
    // Render logic
  };
  
  // 5. Main render
  return (
    <View style={styles.container}>
      {/* Component JSX */}
    </View>
  );
}
```

#### 2. Performance Optimization
```typescript
// ✅ Good: Memoized components
const ScanResultItem = React.memo(({ result }: { result: ScanResult }) => {
  return <View>{/* Component content */}</View>;
});

// ✅ Good: Optimized callbacks
const handlePress = useCallback((id: string) => {
  onItemPress(id);
}, [onItemPress]);

// ✅ Good: Conditional rendering
{isLoading ? <LoadingSpinner /> : <ScanHistory />}
```

### Styling Guidelines

#### 1. StyleSheet Usage
```typescript
// ✅ Good: Organized styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
});
```

#### 2. Responsive Design
```typescript
// ✅ Good: Responsive dimensions
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    width: width * 0.9,
    maxWidth: 400,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridItem: {
    width: (width - 48) / 2, // 2 columns with margins
  },
});
```

## 🧪 Testing Guidelines

### Unit Testing

#### 1. Test Structure
```typescript
describe('QR Utils', () => {
  describe('detectScanType', () => {
    test('should detect URL type', () => {
      // Arrange
      const input = 'https://example.com';
      
      // Act
      const result = detectScanType(input);
      
      // Assert
      expect(result).toBe(ScanType.URL);
    });
  });
});
```

#### 2. Mocking
```typescript
// Mock external dependencies
jest.mock('expo-camera', () => ({
  CameraView: 'CameraView',
  useCameraPermissions: () => [{ granted: true }, jest.fn()],
}));

// Mock database operations
const mockSaveScanResult = jest.fn();
jest.mock('@/utils/database', () => ({
  databaseManager: {
    saveScanResult: mockSaveScanResult,
  },
}));
```

### Integration Testing

#### 1. Component Testing
```typescript
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import QRScanner from '@/components/QRScanner';

test('should call onScanSuccess when QR code is detected', async () => {
  const mockOnScanSuccess = jest.fn();
  
  const { getByTestId } = render(
    <QRScanner onScanSuccess={mockOnScanSuccess} />
  );
  
  // Simulate scan success
  fireEvent(getByTestId('camera-view'), 'onBarcodeScanned', {
    type: 'qr',
    data: 'https://example.com'
  });
  
  await waitFor(() => {
    expect(mockOnScanSuccess).toHaveBeenCalledWith(
      expect.objectContaining({
        data: 'https://example.com',
        type: ScanType.URL
      })
    );
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- QRScanner.test.tsx
```

## 🐛 Debugging

### Development Tools

#### 1. React Native Debugger
```bash
# Install React Native Debugger
npm install -g react-native-debugger

# Start debugger
react-native-debugger
```

#### 2. Flipper Integration
```bash
# Install Flipper
# Download from https://fbflipper.com/

# Enable Flipper in development
# Already configured in metro.config.js
```

#### 3. Console Logging
```typescript
// ✅ Good: Structured logging
console.log('QR Scan Result:', {
  id: result.id,
  type: result.type,
  timestamp: result.timestamp.toISOString()
});

// ✅ Good: Error logging with context
console.error('Database save failed:', {
  error: error.message,
  scanResultId: result.id,
  timestamp: new Date().toISOString()
});
```

### Common Issues

#### 1. Camera Permission Issues
```typescript
// Debug camera permissions
const [permission, requestPermission] = useCameraPermissions();

useEffect(() => {
  console.log('Camera permission status:', permission);
  if (!permission?.granted) {
    console.log('Requesting camera permission...');
    requestPermission();
  }
}, [permission]);
```

#### 2. Database Connection Issues
```typescript
// Debug database operations
try {
  await databaseManager.initialize();
  console.log('Database initialized successfully');
} catch (error) {
  console.error('Database initialization failed:', error);
  // Fallback logic
}
```

## ⚡ Performance Optimization

### 1. Component Optimization
```typescript
// Memoize expensive calculations
const processedData = useMemo(() => {
  return scanHistory.map(item => ({
    ...item,
    formattedData: formatDataForDisplay(item.data, item.type)
  }));
}, [scanHistory]);

// Optimize re-renders
const ScanHistoryItem = React.memo(({ item, onPress }) => {
  return (
    <TouchableOpacity onPress={() => onPress(item.id)}>
      {/* Item content */}
    </TouchableOpacity>
  );
});
```

### 2. Database Optimization
```typescript
// Use pagination for large datasets
const loadHistory = async (page: number = 0, limit: number = 50) => {
  const offset = page * limit;
  return await databaseManager.getScanHistory(
    undefined, // filter
    { field: 'timestamp', direction: 'desc' }, // sort
    limit,
    offset
  );
};

// Batch database operations
const saveBatchResults = async (results: ScanResult[]) => {
  const transaction = await db.transaction();
  try {
    for (const result of results) {
      await databaseManager.saveScanResult(result);
    }
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
```

### 3. Memory Management
```typescript
// Cleanup resources in useEffect
useEffect(() => {
  const subscription = someService.subscribe(handleData);
  
  return () => {
    subscription.unsubscribe();
  };
}, []);

// Optimize image loading
const optimizeImage = (uri: string, maxWidth: number = 800) => {
  return {
    uri,
    width: maxWidth,
    height: maxWidth,
    resizeMode: 'contain' as const
  };
};
```

## 🚀 Deployment

### Build Configuration

#### 1. Environment Variables
```javascript
// app.config.js
export default {
  expo: {
    name: process.env.NODE_ENV === 'production' ? 'QR Scanner Pro' : 'QR Scanner Dev',
    slug: 'qr-scanner-pro',
    version: '1.0.0',
    // ... other config
  }
};
```

#### 2. Build Commands
```bash
# Development build
npm run build:dev

# Production build
npm run build:prod

# Build for specific platform
npx eas build --platform android
npx eas build --platform ios
```

### Release Process

#### 1. Pre-release Checklist
- [ ] All tests passing
- [ ] Code review completed
- [ ] Performance testing done
- [ ] Documentation updated
- [ ] Version number bumped

#### 2. Release Commands
```bash
# Create release build
npm run release

# Submit to app stores
npx eas submit --platform android
npx eas submit --platform ios
```

## 📚 Resources

### Documentation
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

### Tools
- [VS Code Extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next)
- [ESLint Configuration](https://eslint.org/docs/user-guide/getting-started)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)

### Community
- [Expo Discord](https://chat.expo.dev/)
- [React Native Community](https://reactnative.dev/community/overview)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native)
