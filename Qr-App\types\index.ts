/**
 * Definisi tipe data untuk aplikasi QR Scanner Pro
 * File ini berisi semua interface dan type yang digunakan di seluruh aplikasi
 */

// Tipe data untuk hasil scan QR/Barcode
export interface ScanResult {
  id: string;
  data: string;
  type: ScanType;
  format: BarcodeFormat;
  timestamp: Date;
  scanMethod: ScanMethod;
  location?: LocationData;
  isFavorite: boolean;
}

// <PERSON><PERSON>-<PERSON>eni<PERSON> scan yang didukung
export enum ScanType {
  TEXT = 'text',
  URL = 'url',
  EMAIL = 'email',
  PHONE = 'phone',
  SMS = 'sms',
  WIFI = 'wifi',
  CONTACT = 'contact',
  CALENDAR = 'calendar',
  LOCATION = 'location',
  UNKNOWN = 'unknown'
}

// Format barcode yang didukung
export enum BarcodeFormat {
  QR_CODE = 'qr_code',
  AZTEC = 'aztec',
  CODABAR = 'codabar',
  CODE_39 = 'code_39',
  CODE_93 = 'code_93',
  CODE_128 = 'code_128',
  DATA_MATRIX = 'data_matrix',
  EAN_8 = 'ean_8',
  EAN_13 = 'ean_13',
  ITF = 'itf',
  PDF_417 = 'pdf_417',
  UPC_A = 'upc_a',
  UPC_E = 'upc_e'
}

// Metode scanning
export enum ScanMethod {
  CAMERA = 'camera',
  GALLERY = 'gallery'
}

// Data lokasi (opsional)
export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

// Konfigurasi QR Code Generator
export interface QRGeneratorConfig {
  size: number;
  errorCorrectionLevel: ErrorCorrectionLevel;
  foregroundColor: string;
  backgroundColor: string;
  logo?: string;
  logoSize?: number;
  quietZone: number;
}

// Level koreksi error untuk QR Code
export enum ErrorCorrectionLevel {
  L = 'L', // 7%
  M = 'M', // 15%
  Q = 'Q', // 25%
  H = 'H'  // 30%
}

// Data kontak untuk vCard
export interface ContactData {
  firstName: string;
  lastName: string;
  organization?: string;
  phone?: string;
  email?: string;
  website?: string;
  address?: string;
}

// Data WiFi
export interface WiFiData {
  ssid: string;
  password: string;
  security: WiFiSecurity;
  hidden?: boolean;
}

// Jenis keamanan WiFi
export enum WiFiSecurity {
  WPA = 'WPA',
  WEP = 'WEP',
  NONE = 'nopass'
}

// Pengaturan aplikasi
export interface AppSettings {
  enableSound: boolean;
  enableVibration: boolean;
  enableFlashlight: boolean;
  autoSave: boolean;
  historyRetentionDays: number;
  defaultQRSize: number;
  theme: 'light' | 'dark' | 'system';
}

// Filter untuk history
export interface HistoryFilter {
  type?: ScanType;
  dateFrom?: Date;
  dateTo?: Date;
  searchQuery?: string;
  isFavorite?: boolean;
}

// Opsi sorting untuk history
export interface SortOptions {
  field: 'timestamp' | 'type' | 'data';
  direction: 'asc' | 'desc';
}
