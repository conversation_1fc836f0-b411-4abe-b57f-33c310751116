/**
 * Utility functions untuk QR Code dan Barcode processing
 * Berisi fungsi-fungsi helper untuk parsing, validasi, dan manipulasi data QR/Barcode
 */

import { ScanType, ContactData, WiFiData, WiFiSecurity } from '../types';

/**
 * Mendeteksi tipe data dari hasil scan QR/Barcode
 */
export function detectScanType(data: string): ScanType {
  // URL detection
  if (isValidUrl(data)) {
    return ScanType.URL;
  }

  // Email detection
  if (data.toLowerCase().startsWith('mailto:') || isValidEmail(data)) {
    return ScanType.EMAIL;
  }

  // Phone detection
  if (data.toLowerCase().startsWith('tel:') || isValidPhone(data)) {
    return ScanType.PHONE;
  }

  // SMS detection
  if (data.toLowerCase().startsWith('sms:') || data.toLowerCase().startsWith('smsto:')) {
    return ScanType.SMS;
  }

  // WiFi detection
  if (data.toLowerCase().startsWith('wifi:')) {
    return ScanType.WIFI;
  }

  // vCard contact detection
  if (data.toLowerCase().startsWith('begin:vcard')) {
    return ScanType.CONTACT;
  }

  // Calendar event detection
  if (data.toLowerCase().startsWith('begin:vevent')) {
    return ScanType.CALENDAR;
  }

  // Location detection (geo:)
  if (data.toLowerCase().startsWith('geo:')) {
    return ScanType.LOCATION;
  }

  // Default to text
  return ScanType.TEXT;
}

/**
 * Validasi URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Validasi email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validasi nomor telepon
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Parse data WiFi dari QR code
 */
export function parseWiFiData(data: string): WiFiData | null {
  if (!data.toLowerCase().startsWith('wifi:')) {
    return null;
  }

  try {
    const wifiString = data.substring(5); // Remove 'WIFI:'
    const params: { [key: string]: string } = {};
    
    // Parse parameters
    const matches = wifiString.match(/([TSPH]):(.*?)(?=;[TSPH]:|$)/g);
    if (!matches) return null;

    matches.forEach(match => {
      const [key, ...valueParts] = match.split(':');
      const value = valueParts.join(':').replace(/;$/, '');
      params[key] = value;
    });

    return {
      ssid: params.S || '',
      password: params.P || '',
      security: (params.T as WiFiSecurity) || WiFiSecurity.WPA,
      hidden: params.H === 'true'
    };
  } catch (error) {
    console.error('Error parsing WiFi data:', error);
    return null;
  }
}

/**
 * Generate WiFi QR code string
 */
export function generateWiFiQRString(wifiData: WiFiData): string {
  const { ssid, password, security, hidden } = wifiData;
  return `WIFI:T:${security};S:${ssid};P:${password};H:${hidden ? 'true' : 'false'};;`;
}

/**
 * Parse vCard contact data
 */
export function parseVCardData(data: string): ContactData | null {
  if (!data.toLowerCase().startsWith('begin:vcard')) {
    return null;
  }

  try {
    const lines = data.split('\n');
    const contact: Partial<ContactData> = {};

    lines.forEach(line => {
      const [key, value] = line.split(':');
      if (!key || !value) return;

      const cleanKey = key.toLowerCase().replace(/;.*/, '');
      
      switch (cleanKey) {
        case 'fn':
          const names = value.split(' ');
          contact.firstName = names[0] || '';
          contact.lastName = names.slice(1).join(' ') || '';
          break;
        case 'org':
          contact.organization = value;
          break;
        case 'tel':
          contact.phone = value;
          break;
        case 'email':
          contact.email = value;
          break;
        case 'url':
          contact.website = value;
          break;
        case 'adr':
          contact.address = value.replace(/;/g, ', ');
          break;
      }
    });

    return contact as ContactData;
  } catch (error) {
    console.error('Error parsing vCard data:', error);
    return null;
  }
}

/**
 * Generate vCard string dari contact data
 */
export function generateVCardString(contact: ContactData): string {
  let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';
  
  if (contact.firstName || contact.lastName) {
    vcard += `FN:${contact.firstName} ${contact.lastName}\n`;
  }
  
  if (contact.organization) {
    vcard += `ORG:${contact.organization}\n`;
  }
  
  if (contact.phone) {
    vcard += `TEL:${contact.phone}\n`;
  }
  
  if (contact.email) {
    vcard += `EMAIL:${contact.email}\n`;
  }
  
  if (contact.website) {
    vcard += `URL:${contact.website}\n`;
  }
  
  if (contact.address) {
    vcard += `ADR:;;${contact.address};;;;\n`;
  }
  
  vcard += 'END:VCARD';
  return vcard;
}

/**
 * Format data untuk display yang user-friendly
 */
export function formatDataForDisplay(data: string, type: ScanType): string {
  switch (type) {
    case ScanType.URL:
      return data.length > 50 ? `${data.substring(0, 50)}...` : data;
    
    case ScanType.EMAIL:
      return data.replace('mailto:', '');
    
    case ScanType.PHONE:
      return data.replace('tel:', '');
    
    case ScanType.SMS:
      return data.replace(/^sms(to)?:/, '');
    
    case ScanType.WIFI:
      const wifiData = parseWiFiData(data);
      return wifiData ? `WiFi: ${wifiData.ssid}` : data;
    
    case ScanType.CONTACT:
      const contactData = parseVCardData(data);
      return contactData ? `${contactData.firstName} ${contactData.lastName}`.trim() : 'Contact';
    
    case ScanType.LOCATION:
      return data.replace('geo:', 'Location: ');
    
    default:
      return data.length > 100 ? `${data.substring(0, 100)}...` : data;
  }
}

/**
 * Generate unique ID untuk scan result
 */
export function generateScanId(): string {
  return `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validasi dan sanitasi input untuk QR generation
 */
export function sanitizeQRInput(input: string): string {
  // Remove null characters dan control characters
  return input.replace(/[\x00-\x1F\x7F]/g, '');
}

/**
 * Estimate QR code complexity berdasarkan data length
 */
export function estimateQRComplexity(data: string): 'low' | 'medium' | 'high' {
  const length = data.length;
  
  if (length < 100) return 'low';
  if (length < 500) return 'medium';
  return 'high';
}

/**
 * Get recommended error correction level berdasarkan use case
 */
export function getRecommendedErrorCorrection(type: ScanType): 'L' | 'M' | 'Q' | 'H' {
  switch (type) {
    case ScanType.URL:
    case ScanType.WIFI:
      return 'M'; // Medium untuk data penting
    
    case ScanType.CONTACT:
    case ScanType.EMAIL:
      return 'Q'; // Quartile untuk data yang sering di-scan
    
    default:
      return 'L'; // Low untuk text biasa
  }
}
