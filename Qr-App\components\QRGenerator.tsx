/**
 * Komponen untuk generate QR Code
 * Mendukung berbagai tipe data: text, URL, contact, WiFi dengan customization options
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  Modal
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import {
  QRGeneratorConfig,
  ErrorCorrectionLevel,
  ContactData,
  WiFiData,
  WiFiSecurity
} from '../types';
import {
  generateVCardString,
  generateWiFiQRString,
  sanitizeQRInput
} from '../utils/qrUtils';

type QRType = 'text' | 'url' | 'contact' | 'wifi';

interface QRGeneratorProps {
  onGenerated?: (data: string, type: QRType) => void;
}

/**
 * Komponen untuk generate QR Code dengan berbagai tipe data
 */
export default function QRGenerator({ onGenerated }: QRGeneratorProps) {
  const [selectedType, setSelectedType] = useState<QRType>('text');
  const [qrData, setQrData] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [showCustomization, setShowCustomization] = useState(false);

  // Text input
  const [textInput, setTextInput] = useState('');

  // URL input
  const [urlInput, setUrlInput] = useState('');

  // Contact input
  const [contactData, setContactData] = useState<ContactData>({
    firstName: '',
    lastName: '',
    organization: '',
    phone: '',
    email: '',
    website: '',
    address: ''
  });

  // WiFi input
  const [wifiData, setWifiData] = useState<WiFiData>({
    ssid: '',
    password: '',
    security: WiFiSecurity.WPA,
    hidden: false
  });

  // QR Configuration
  const [qrConfig, setQrConfig] = useState<QRGeneratorConfig>({
    size: 200,
    errorCorrectionLevel: ErrorCorrectionLevel.M,
    foregroundColor: '#000000',
    backgroundColor: '#FFFFFF',
    quietZone: 10
  });

  const qrRef = useRef<any>(null);

  /**
   * Generate QR code berdasarkan tipe yang dipilih
   */
  const generateQR = () => {
    let data = '';

    try {
      switch (selectedType) {
        case 'text':
          if (!textInput.trim()) {
            Alert.alert('Error', 'Masukkan teks yang akan di-generate');
            return;
          }
          data = sanitizeQRInput(textInput);
          break;

        case 'url':
          if (!urlInput.trim()) {
            Alert.alert('Error', 'Masukkan URL yang akan di-generate');
            return;
          }
          // Tambahkan https:// jika tidak ada protocol
          const url = urlInput.startsWith('http') ? urlInput : `https://${urlInput}`;
          data = sanitizeQRInput(url);
          break;

        case 'contact':
          if (!contactData.firstName && !contactData.lastName) {
            Alert.alert('Error', 'Masukkan minimal nama depan atau nama belakang');
            return;
          }
          data = generateVCardString(contactData);
          break;

        case 'wifi':
          if (!wifiData.ssid.trim()) {
            Alert.alert('Error', 'Masukkan nama WiFi (SSID)');
            return;
          }
          data = generateWiFiQRString(wifiData);
          break;

        default:
          Alert.alert('Error', 'Tipe QR Code tidak valid');
          return;
      }

      setQrData(data);
      setShowPreview(true);
      onGenerated?.(data, selectedType);

    } catch (error) {
      console.error('Error generating QR:', error);
      Alert.alert('Error', 'Gagal generate QR Code');
    }
  };

  /**
   * Save QR code ke gallery
   */
  const saveQRCode = async () => {
    try {
      if (!qrRef.current) {
        Alert.alert('Error', 'QR Code belum di-generate');
        return;
      }

      // Request permission
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Aplikasi memerlukan akses ke gallery untuk menyimpan QR Code');
        return;
      }

      // Generate SVG as base64
      qrRef.current.toDataURL((dataURL: string) => {
        const filename = `qr_code_${Date.now()}.png`;
        const fileUri = `${FileSystem.documentDirectory}${filename}`;

        // Convert base64 to file
        FileSystem.writeAsStringAsync(fileUri, dataURL.split(',')[1], {
          encoding: FileSystem.EncodingType.Base64,
        }).then(() => {
          // Save to gallery
          return MediaLibrary.saveToLibraryAsync(fileUri);
        }).then(() => {
          Alert.alert('Berhasil', 'QR Code berhasil disimpan ke gallery');
        }).catch((error) => {
          console.error('Error saving QR code:', error);
          Alert.alert('Error', 'Gagal menyimpan QR Code');
        });
      });

    } catch (error) {
      console.error('Error saving QR code:', error);
      Alert.alert('Error', 'Gagal menyimpan QR Code');
    }
  };

  /**
   * Share QR code
   */
  const shareQRCode = async () => {
    try {
      if (!qrRef.current) {
        Alert.alert('Error', 'QR Code belum di-generate');
        return;
      }

      qrRef.current.toDataURL((dataURL: string) => {
        const filename = `qr_code_${Date.now()}.png`;
        const fileUri = `${FileSystem.documentDirectory}${filename}`;

        FileSystem.writeAsStringAsync(fileUri, dataURL.split(',')[1], {
          encoding: FileSystem.EncodingType.Base64,
        }).then(() => {
          return Sharing.shareAsync(fileUri);
        }).catch((error) => {
          console.error('Error sharing QR code:', error);
          Alert.alert('Error', 'Gagal share QR Code');
        });
      });

    } catch (error) {
      console.error('Error sharing QR code:', error);
      Alert.alert('Error', 'Gagal share QR Code');
    }
  };

  /**
   * Reset form
   */
  const resetForm = () => {
    setTextInput('');
    setUrlInput('');
    setContactData({
      firstName: '',
      lastName: '',
      organization: '',
      phone: '',
      email: '',
      website: '',
      address: ''
    });
    setWifiData({
      ssid: '',
      password: '',
      security: WiFiSecurity.WPA,
      hidden: false
    });
    setQrData('');
    setShowPreview(false);
  };

  /**
   * Render input form berdasarkan tipe yang dipilih
   */
  const renderInputForm = () => {
    switch (selectedType) {
      case 'text':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Teks</Text>
            <TextInput
              style={[styles.textInput, styles.multilineInput]}
              placeholder="Masukkan teks yang akan di-generate..."
              value={textInput}
              onChangeText={setTextInput}
              multiline
              numberOfLines={4}
              placeholderTextColor="#999999"
            />
            <Text style={styles.characterCount}>
              {textInput.length} karakter
            </Text>
          </View>
        );

      case 'url':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>URL</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Masukkan URL (contoh: google.com)"
              value={urlInput}
              onChangeText={setUrlInput}
              keyboardType="url"
              autoCapitalize="none"
              placeholderTextColor="#999999"
            />
          </View>
        );

      case 'contact':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.sectionTitle}>Informasi Kontak</Text>

            <View style={styles.row}>
              <View style={styles.halfInput}>
                <Text style={styles.inputLabel}>Nama Depan *</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Nama depan"
                  value={contactData.firstName}
                  onChangeText={(text) => setContactData({...contactData, firstName: text})}
                  placeholderTextColor="#999999"
                />
              </View>
              <View style={styles.halfInput}>
                <Text style={styles.inputLabel}>Nama Belakang</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Nama belakang"
                  value={contactData.lastName}
                  onChangeText={(text) => setContactData({...contactData, lastName: text})}
                  placeholderTextColor="#999999"
                />
              </View>
            </View>

            <Text style={styles.inputLabel}>Organisasi</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Nama perusahaan/organisasi"
              value={contactData.organization}
              onChangeText={(text) => setContactData({...contactData, organization: text})}
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Telepon</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Nomor telepon"
              value={contactData.phone}
              onChangeText={(text) => setContactData({...contactData, phone: text})}
              keyboardType="phone-pad"
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Alamat email"
              value={contactData.email}
              onChangeText={(text) => setContactData({...contactData, email: text})}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Website</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Website URL"
              value={contactData.website}
              onChangeText={(text) => setContactData({...contactData, website: text})}
              keyboardType="url"
              autoCapitalize="none"
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Alamat</Text>
            <TextInput
              style={[styles.textInput, styles.multilineInput]}
              placeholder="Alamat lengkap"
              value={contactData.address}
              onChangeText={(text) => setContactData({...contactData, address: text})}
              multiline
              numberOfLines={3}
              placeholderTextColor="#999999"
            />
          </View>
        );

      case 'wifi':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.sectionTitle}>Informasi WiFi</Text>

            <Text style={styles.inputLabel}>Nama WiFi (SSID) *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Nama jaringan WiFi"
              value={wifiData.ssid}
              onChangeText={(text) => setWifiData({...wifiData, ssid: text})}
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Password WiFi"
              value={wifiData.password}
              onChangeText={(text) => setWifiData({...wifiData, password: text})}
              secureTextEntry
              placeholderTextColor="#999999"
            />

            <Text style={styles.inputLabel}>Keamanan</Text>
            <View style={styles.securityContainer}>
              {Object.values(WiFiSecurity).map((security) => (
                <TouchableOpacity
                  key={security}
                  style={[
                    styles.securityOption,
                    wifiData.security === security && styles.securityOptionSelected
                  ]}
                  onPress={() => setWifiData({...wifiData, security})}
                >
                  <Text style={[
                    styles.securityOptionText,
                    wifiData.security === security && styles.securityOptionTextSelected
                  ]}>
                    {security}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.switchContainer}>
              <Text style={styles.inputLabel}>Jaringan Tersembunyi</Text>
              <Switch
                value={wifiData.hidden}
                onValueChange={(value) => setWifiData({...wifiData, hidden: value})}
                trackColor={{ false: '#E5E5E7', true: '#007AFF' }}
                thumbColor="#FFFFFF"
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>QR Code Generator</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => setShowCustomization(true)}
        >
          <Ionicons name="settings" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Type selector */}
        <View style={styles.typeSelector}>
          <Text style={styles.sectionTitle}>Pilih Tipe QR Code</Text>
          <View style={styles.typeButtons}>
            {[
              { key: 'text', label: 'Teks', icon: 'document-text' },
              { key: 'url', label: 'URL', icon: 'link' },
              { key: 'contact', label: 'Kontak', icon: 'person' },
              { key: 'wifi', label: 'WiFi', icon: 'wifi' }
            ].map((type) => (
              <TouchableOpacity
                key={type.key}
                style={[
                  styles.typeButton,
                  selectedType === type.key && styles.typeButtonSelected
                ]}
                onPress={() => {
                  setSelectedType(type.key as QRType);
                  resetForm();
                }}
              >
                <Ionicons
                  name={type.icon as any}
                  size={20}
                  color={selectedType === type.key ? '#FFFFFF' : '#007AFF'}
                />
                <Text style={[
                  styles.typeButtonText,
                  selectedType === type.key && styles.typeButtonTextSelected
                ]}>
                  {type.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Input form */}
        {renderInputForm()}

        {/* Action buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.button, styles.generateButton]}
            onPress={generateQR}
          >
            <Ionicons name="qr-code" size={20} color="#FFFFFF" />
            <Text style={styles.buttonText}>Generate QR Code</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.resetButton]}
            onPress={resetForm}
          >
            <Ionicons name="refresh" size={20} color="#FF3B30" />
            <Text style={[styles.buttonText, { color: '#FF3B30' }]}>Reset</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* QR Preview Modal */}
      <Modal
        visible={showPreview}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPreview(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.previewModal}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewTitle}>QR Code Preview</Text>
              <TouchableOpacity onPress={() => setShowPreview(false)}>
                <Ionicons name="close" size={24} color="#666666" />
              </TouchableOpacity>
            </View>

            <View style={styles.qrContainer}>
              {qrData && (
                <QRCode
                  ref={qrRef}
                  value={qrData}
                  size={qrConfig.size}
                  color={qrConfig.foregroundColor}
                  backgroundColor={qrConfig.backgroundColor}
                  errorCorrectionLevel={qrConfig.errorCorrectionLevel}
                  quietZone={qrConfig.quietZone}
                />
              )}
            </View>

            <View style={styles.previewActions}>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={saveQRCode}
              >
                <Ionicons name="download" size={20} color="#FFFFFF" />
                <Text style={styles.buttonText}>Simpan</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.shareButton]}
                onPress={shareQRCode}
              >
                <Ionicons name="share" size={20} color="#FFFFFF" />
                <Text style={styles.buttonText}>Share</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

/**
 * Styles untuk komponen QRGenerator
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
  },
  headerButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  typeSelector: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  typeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    backgroundColor: '#FFFFFF',
    gap: 8,
  },
  typeButtonSelected: {
    backgroundColor: '#007AFF',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  typeButtonTextSelected: {
    color: '#FFFFFF',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#FFFFFF',
    marginBottom: 12,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: -8,
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfInput: {
    flex: 1,
  },
  securityContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  securityOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E5E7',
    backgroundColor: '#FFFFFF',
  },
  securityOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  securityOptionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
  },
  securityOptionTextSelected: {
    color: '#FFFFFF',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 20,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  generateButton: {
    backgroundColor: '#007AFF',
  },
  resetButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  saveButton: {
    backgroundColor: '#34C759',
  },
  shareButton: {
    backgroundColor: '#007AFF',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  previewModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
    padding: 20,
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
  },
});
