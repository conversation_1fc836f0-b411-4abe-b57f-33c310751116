/**
 * Unit tests untuk QR Utils
 * Testing berbagai fungsi utility untuk QR Code processing
 */

import {
  detectScanType,
  isValidUrl,
  isValidEmail,
  isValidPhone,
  parseWiFiData,
  parseVCardData,
  generateVCardString,
  generateWiFiQRString,
  formatDataForDisplay,
  generateScanId,
  sanitizeQRInput
} from '../../utils/qrUtils';
import { ScanType, WiFiSecurity } from '../../types';

describe('QR Utils', () => {
  describe('detectScanType', () => {
    test('should detect URL type', () => {
      expect(detectScanType('https://google.com')).toBe(ScanType.URL);
      expect(detectScanType('http://example.com')).toBe(ScanType.URL);
      expect(detectScanType('ftp://files.example.com')).toBe(ScanType.URL);
    });

    test('should detect email type', () => {
      expect(detectScanType('mailto:<EMAIL>')).toBe(ScanType.EMAIL);
      expect(detectScanType('<EMAIL>')).toBe(ScanType.EMAIL);
    });

    test('should detect phone type', () => {
      expect(detectScanType('tel:+1234567890')).toBe(ScanType.PHONE);
      expect(detectScanType('+1234567890')).toBe(ScanType.PHONE);
    });

    test('should detect SMS type', () => {
      expect(detectScanType('sms:+1234567890')).toBe(ScanType.SMS);
      expect(detectScanType('smsto:+1234567890')).toBe(ScanType.SMS);
    });

    test('should detect WiFi type', () => {
      expect(detectScanType('WIFI:T:WPA;S:MyNetwork;P:password;;')).toBe(ScanType.WIFI);
    });

    test('should detect contact type', () => {
      expect(detectScanType('BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEND:VCARD')).toBe(ScanType.CONTACT);
    });

    test('should detect location type', () => {
      expect(detectScanType('geo:37.7749,-122.4194')).toBe(ScanType.LOCATION);
    });

    test('should default to text type', () => {
      expect(detectScanType('Just some random text')).toBe(ScanType.TEXT);
    });
  });

  describe('isValidUrl', () => {
    test('should validate correct URLs', () => {
      expect(isValidUrl('https://google.com')).toBe(true);
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('ftp://files.example.com')).toBe(true);
    });

    test('should reject invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('just text')).toBe(false);
      expect(isValidUrl('')).toBe(false);
    });
  });

  describe('isValidEmail', () => {
    test('should validate correct emails', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    test('should reject invalid emails', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
    });
  });

  describe('isValidPhone', () => {
    test('should validate correct phone numbers', () => {
      expect(isValidPhone('+1234567890')).toBe(true);
      expect(isValidPhone('1234567890')).toBe(true);
      expect(isValidPhone('+62 812 3456 7890')).toBe(true);
    });

    test('should reject invalid phone numbers', () => {
      expect(isValidPhone('abc123')).toBe(false);
      expect(isValidPhone('123')).toBe(false);
      expect(isValidPhone('')).toBe(false);
    });
  });

  describe('parseWiFiData', () => {
    test('should parse valid WiFi QR data', () => {
      const wifiString = 'WIFI:T:WPA;S:MyNetwork;P:password123;H:false;;';
      const result = parseWiFiData(wifiString);
      
      expect(result).toEqual({
        ssid: 'MyNetwork',
        password: 'password123',
        security: WiFiSecurity.WPA,
        hidden: false
      });
    });

    test('should return null for invalid WiFi data', () => {
      expect(parseWiFiData('not-wifi-data')).toBeNull();
      expect(parseWiFiData('WIFI:invalid')).toBeNull();
    });
  });

  describe('parseVCardData', () => {
    test('should parse valid vCard data', () => {
      const vCardString = `BEGIN:VCARD
VERSION:3.0
FN:John Doe
ORG:Example Corp
TEL:+1234567890
EMAIL:<EMAIL>
END:VCARD`;

      const result = parseVCardData(vCardString);
      
      expect(result).toEqual({
        firstName: 'John',
        lastName: 'Doe',
        organization: 'Example Corp',
        phone: '+1234567890',
        email: '<EMAIL>'
      });
    });

    test('should return null for invalid vCard data', () => {
      expect(parseVCardData('not-vcard-data')).toBeNull();
    });
  });

  describe('generateVCardString', () => {
    test('should generate valid vCard string', () => {
      const contactData = {
        firstName: 'John',
        lastName: 'Doe',
        organization: 'Example Corp',
        phone: '+1234567890',
        email: '<EMAIL>'
      };

      const result = generateVCardString(contactData);
      
      expect(result).toContain('BEGIN:VCARD');
      expect(result).toContain('FN:John Doe');
      expect(result).toContain('ORG:Example Corp');
      expect(result).toContain('TEL:+1234567890');
      expect(result).toContain('EMAIL:<EMAIL>');
      expect(result).toContain('END:VCARD');
    });
  });

  describe('generateWiFiQRString', () => {
    test('should generate valid WiFi QR string', () => {
      const wifiData = {
        ssid: 'MyNetwork',
        password: 'password123',
        security: WiFiSecurity.WPA,
        hidden: false
      };

      const result = generateWiFiQRString(wifiData);
      
      expect(result).toBe('WIFI:T:WPA;S:MyNetwork;P:password123;H:false;;');
    });
  });

  describe('formatDataForDisplay', () => {
    test('should format URL data', () => {
      const longUrl = 'https://very-long-url-that-should-be-truncated-for-display.com/path/to/resource';
      const result = formatDataForDisplay(longUrl, ScanType.URL);
      
      expect(result.length).toBeLessThanOrEqual(53); // 50 chars + "..."
    });

    test('should format email data', () => {
      const result = formatDataForDisplay('mailto:<EMAIL>', ScanType.EMAIL);
      expect(result).toBe('<EMAIL>');
    });

    test('should format phone data', () => {
      const result = formatDataForDisplay('tel:+1234567890', ScanType.PHONE);
      expect(result).toBe('+1234567890');
    });

    test('should format text data', () => {
      const longText = 'A'.repeat(150);
      const result = formatDataForDisplay(longText, ScanType.TEXT);
      
      expect(result.length).toBeLessThanOrEqual(103); // 100 chars + "..."
    });
  });

  describe('generateScanId', () => {
    test('should generate unique scan IDs', () => {
      const id1 = generateScanId();
      const id2 = generateScanId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^scan_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^scan_\d+_[a-z0-9]+$/);
    });
  });

  describe('sanitizeQRInput', () => {
    test('should remove control characters', () => {
      const input = 'Hello\x00World\x1F';
      const result = sanitizeQRInput(input);
      
      expect(result).toBe('HelloWorld');
    });

    test('should preserve normal characters', () => {
      const input = 'Hello World! 123 @#$%';
      const result = sanitizeQRInput(input);
      
      expect(result).toBe(input);
    });
  });
});
