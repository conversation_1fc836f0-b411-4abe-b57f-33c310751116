/**
 * Komponen QR Scanner utama
 * Menangani real-time camera scanning dengan fitur auto-focus, zoom, flashlight, dan feedback
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
  Animated,
  Platform
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { BarCodeScanningResult } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import { Audio } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { ScanResult, ScanMethod, BarcodeFormat } from '../types';
import { detectScanType, generateScanId } from '../utils/qrUtils';
import { databaseManager } from '../utils/database';

const { width, height } = Dimensions.get('window');

interface QRScannerProps {
  onScanSuccess: (result: ScanResult) => void;
  onError?: (error: string) => void;
  enableSound?: boolean;
  enableVibration?: boolean;
  autoSave?: boolean;
}

/**
 * Komponen utama untuk scanning QR Code dan Barcode
 */
export default function QRScanner({
  onScanSuccess,
  onError,
  enableSound = true,
  enableVibration = true,
  autoSave = true
}: QRScannerProps) {
  // State management
  const [permission, requestPermission] = useCameraPermissions();
  const [hasFlashlight, setHasFlashlight] = useState(false);
  const [flashlightOn, setFlashlightOn] = useState(false);
  const [isScanning, setIsScanning] = useState(true);
  const [lastScanTime, setLastScanTime] = useState(0);
  const [zoom, setZoom] = useState(0);

  // Animation untuk scan overlay
  const scanAnimation = useRef(new Animated.Value(0)).current;
  const successAnimation = useRef(new Animated.Value(0)).current;

  // Sound untuk feedback
  const [scanSound, setScanSound] = useState<Audio.Sound | null>(null);

  /**
   * Inisialisasi komponen
   */
  useEffect(() => {
    initializeScanner();
    startScanAnimation();

    return () => {
      cleanup();
    };
  }, []);

  /**
   * Inisialisasi scanner dan load sound
   */
  const initializeScanner = async () => {
    try {
      // Load scan sound
      if (enableSound) {
        const { sound } = await Audio.Sound.createAsync(
          require('../assets/sounds/beep.mp3'), // Anda perlu menambahkan file sound ini
          { shouldPlay: false }
        );
        setScanSound(sound);
      }

      // Check flashlight availability
      setHasFlashlight(Platform.OS === 'ios' || Platform.OS === 'android');
    } catch (error) {
      console.error('Error initializing scanner:', error);
    }
  };

  /**
   * Cleanup resources
   */
  const cleanup = async () => {
    if (scanSound) {
      await scanSound.unloadAsync();
    }
  };

  /**
   * Animasi scanning overlay
   */
  const startScanAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scanAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scanAnimation, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  /**
   * Animasi success feedback
   */
  const playSuccessAnimation = () => {
    Animated.sequence([
      Animated.timing(successAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(successAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  /**
   * Handle hasil scan barcode
   */
  const handleBarCodeScanned = async ({ type, data }: BarCodeScanningResult) => {
    // Prevent multiple scans dalam waktu singkat
    const currentTime = Date.now();
    if (currentTime - lastScanTime < 2000) {
      return;
    }
    setLastScanTime(currentTime);

    try {
      // Pause scanning sementara
      setIsScanning(false);

      // Feedback untuk user
      await provideFeedback();

      // Buat scan result object
      const scanResult: ScanResult = {
        id: generateScanId(),
        data: data,
        type: detectScanType(data),
        format: mapBarcodeFormat(type),
        timestamp: new Date(),
        scanMethod: ScanMethod.CAMERA,
        isFavorite: false
      };

      // Auto save ke database jika diaktifkan
      if (autoSave) {
        await databaseManager.saveScanResult(scanResult);
      }

      // Callback ke parent component
      onScanSuccess(scanResult);

      // Resume scanning setelah delay
      setTimeout(() => {
        setIsScanning(true);
      }, 3000);

    } catch (error) {
      console.error('Error handling scan result:', error);
      onError?.('Gagal memproses hasil scan');
      setIsScanning(true);
    }
  };

  /**
   * Memberikan feedback audio, haptic, dan visual
   */
  const provideFeedback = async () => {
    try {
      // Haptic feedback
      if (enableVibration) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      // Audio feedback
      if (enableSound && scanSound) {
        await scanSound.replayAsync();
      }

      // Visual feedback
      playSuccessAnimation();
    } catch (error) {
      console.error('Error providing feedback:', error);
    }
  };

  /**
   * Map barcode type ke format yang kita gunakan
   */
  const mapBarcodeFormat = (type: string): BarcodeFormat => {
    const formatMap: { [key: string]: BarcodeFormat } = {
      'qr': BarcodeFormat.QR_CODE,
      'aztec': BarcodeFormat.AZTEC,
      'codabar': BarcodeFormat.CODABAR,
      'code39': BarcodeFormat.CODE_39,
      'code93': BarcodeFormat.CODE_93,
      'code128': BarcodeFormat.CODE_128,
      'datamatrix': BarcodeFormat.DATA_MATRIX,
      'ean8': BarcodeFormat.EAN_8,
      'ean13': BarcodeFormat.EAN_13,
      'itf14': BarcodeFormat.ITF,
      'pdf417': BarcodeFormat.PDF_417,
      'upc_a': BarcodeFormat.UPC_A,
      'upc_e': BarcodeFormat.UPC_E,
    };

    return formatMap[type.toLowerCase()] || BarcodeFormat.QR_CODE;
  };

  /**
   * Toggle flashlight
   */
  const toggleFlashlight = () => {
    setFlashlightOn(!flashlightOn);
  };

  /**
   * Handle zoom gesture
   */
  const handleZoomChange = (newZoom: number) => {
    setZoom(Math.max(0, Math.min(1, newZoom)));
  };

  /**
   * Request camera permission jika belum ada
   */
  if (!permission) {
    return <View style={styles.container} />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Aplikasi memerlukan akses kamera untuk scan QR Code
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>Berikan Izin Kamera</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        style={styles.camera}
        facing="back"
        onBarcodeScanned={isScanning ? handleBarCodeScanned : undefined}
        barcodeScannerSettings={{
          barcodeTypes: [
            'qr',
            'aztec',
            'ean13',
            'ean8',
            'upc_a',
            'upc_e',
            'code39',
            'code93',
            'code128',
            'codabar',
            'itf14',
            'datamatrix',
            'pdf417'
          ],
        }}
        enableTorch={flashlightOn}
        zoom={zoom}
      >
        {/* Scan Overlay */}
        <View style={styles.overlay}>
          <View style={styles.scanArea}>
            {/* Corner indicators */}
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />

            {/* Animated scan line */}
            <Animated.View
              style={[
                styles.scanLine,
                {
                  transform: [
                    {
                      translateY: scanAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, 200],
                      }),
                    },
                  ],
                },
              ]}
            />
          </View>
        </View>

        {/* Success overlay */}
        <Animated.View
          style={[
            styles.successOverlay,
            {
              opacity: successAnimation,
            },
          ]}
        />

        {/* Controls */}
        <View style={styles.controls}>
          {/* Flashlight button */}
          {hasFlashlight && (
            <TouchableOpacity
              style={[styles.controlButton, flashlightOn && styles.controlButtonActive]}
              onPress={toggleFlashlight}
            >
              <Ionicons
                name={flashlightOn ? 'flash' : 'flash-off'}
                size={24}
                color={flashlightOn ? '#FFD700' : '#FFFFFF'}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Instructions */}
        <View style={styles.instructions}>
          <Text style={styles.instructionText}>
            Arahkan kamera ke QR Code atau Barcode
          </Text>
          {!isScanning && (
            <Text style={styles.processingText}>
              Memproses hasil scan...
            </Text>
          )}
        </View>
      </CameraView>
    </View>
  );
}

/**
 * Styles untuk komponen QRScanner
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  camera: {
    flex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    padding: 20,
  },
  permissionText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: '#00FF00',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: '#00FF00',
    shadowColor: '#00FF00',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
  },
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 255, 0, 0.3)',
  },
  controls: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    flexDirection: 'column',
    alignItems: 'center',
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  controlButtonActive: {
    backgroundColor: 'rgba(255, 215, 0, 0.3)',
    borderColor: '#FFD700',
  },
  instructions: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  instructionText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  processingText: {
    color: '#FFD700',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
});
