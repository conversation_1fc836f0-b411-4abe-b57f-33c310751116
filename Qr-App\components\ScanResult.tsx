/**
 * Komponen untuk menampilkan hasil scan QR Code/Barcode
 * Menampilkan data yang di-scan dengan aksi yang sesuai berdasarkan tipe data
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import { ScanResult as ScanResultType, ScanType } from '../types';
import { formatDataForDisplay, parseWiFiData, parseVCardData } from '../utils/qrUtils';
import ShareModal from './ShareModal';

interface ScanResultProps {
  scanResult: ScanResultType;
  onClose: () => void;
  onSave?: () => void;
  onFavorite?: () => void;
}

/**
 * Komponen untuk menampilkan dan mengelola hasil scan
 */
export default function ScanResult({
  scanResult,
  onClose,
  onSave,
  onFavorite
}: ScanResultProps) {
  const [showShareModal, setShowShareModal] = useState(false);

  /**
   * Copy data ke clipboard
   */
  const copyToClipboard = async () => {
    try {
      await Clipboard.setStringAsync(scanResult.data);
      Alert.alert('Berhasil', 'Data berhasil disalin ke clipboard');
    } catch (error) {
      Alert.alert('Error', 'Gagal menyalin data');
    }
  };

  /**
   * Show share modal
   */
  const showShare = () => {
    setShowShareModal(true);
  };

  /**
   * Handle aksi berdasarkan tipe data
   */
  const handlePrimaryAction = async () => {
    try {
      switch (scanResult.type) {
        case ScanType.URL:
          const supported = await Linking.canOpenURL(scanResult.data);
          if (supported) {
            await Linking.openURL(scanResult.data);
          } else {
            Alert.alert('Error', 'Tidak dapat membuka URL');
          }
          break;

        case ScanType.EMAIL:
          const emailUrl = scanResult.data.startsWith('mailto:')
            ? scanResult.data
            : `mailto:${scanResult.data}`;
          await Linking.openURL(emailUrl);
          break;

        case ScanType.PHONE:
          const phoneUrl = scanResult.data.startsWith('tel:')
            ? scanResult.data
            : `tel:${scanResult.data}`;
          await Linking.openURL(phoneUrl);
          break;

        case ScanType.SMS:
          const smsUrl = scanResult.data.startsWith('sms')
            ? scanResult.data
            : `sms:${scanResult.data}`;
          await Linking.openURL(smsUrl);
          break;

        case ScanType.WIFI:
          showWiFiInfo();
          break;

        case ScanType.CONTACT:
          showContactInfo();
          break;

        case ScanType.LOCATION:
          const geoUrl = scanResult.data.startsWith('geo:')
            ? scanResult.data
            : `geo:${scanResult.data}`;
          await Linking.openURL(geoUrl);
          break;

        default:
          copyToClipboard();
          break;
      }
    } catch (error) {
      console.error('Error handling primary action:', error);
      Alert.alert('Error', 'Gagal menjalankan aksi');
    }
  };

  /**
   * Tampilkan informasi WiFi
   */
  const showWiFiInfo = () => {
    const wifiData = parseWiFiData(scanResult.data);
    if (wifiData) {
      Alert.alert(
        'Informasi WiFi',
        `SSID: ${wifiData.ssid}\nPassword: ${wifiData.password}\nSecurity: ${wifiData.security}`,
        [
          { text: 'Tutup', style: 'cancel' },
          { text: 'Salin Password', onPress: () => Clipboard.setStringAsync(wifiData.password) }
        ]
      );
    }
  };

  /**
   * Tampilkan informasi kontak
   */
  const showContactInfo = () => {
    const contactData = parseVCardData(scanResult.data);
    if (contactData) {
      const contactInfo = [
        contactData.firstName && contactData.lastName && `Nama: ${contactData.firstName} ${contactData.lastName}`,
        contactData.organization && `Organisasi: ${contactData.organization}`,
        contactData.phone && `Telepon: ${contactData.phone}`,
        contactData.email && `Email: ${contactData.email}`,
        contactData.website && `Website: ${contactData.website}`,
        contactData.address && `Alamat: ${contactData.address}`
      ].filter(Boolean).join('\n');

      Alert.alert('Informasi Kontak', contactInfo);
    }
  };

  /**
   * Get icon berdasarkan tipe scan
   */
  const getTypeIcon = (): string => {
    switch (scanResult.type) {
      case ScanType.URL: return 'link';
      case ScanType.EMAIL: return 'mail';
      case ScanType.PHONE: return 'call';
      case ScanType.SMS: return 'chatbubble';
      case ScanType.WIFI: return 'wifi';
      case ScanType.CONTACT: return 'person';
      case ScanType.CALENDAR: return 'calendar';
      case ScanType.LOCATION: return 'location';
      default: return 'document-text';
    }
  };

  /**
   * Get label untuk primary action button
   */
  const getPrimaryActionLabel = (): string => {
    switch (scanResult.type) {
      case ScanType.URL: return 'Buka URL';
      case ScanType.EMAIL: return 'Kirim Email';
      case ScanType.PHONE: return 'Panggil';
      case ScanType.SMS: return 'Kirim SMS';
      case ScanType.WIFI: return 'Lihat Info WiFi';
      case ScanType.CONTACT: return 'Lihat Kontak';
      case ScanType.LOCATION: return 'Buka Lokasi';
      default: return 'Salin';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.modal}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Ionicons name={getTypeIcon()} size={24} color="#007AFF" />
            <Text style={styles.headerTitle}>Hasil Scan</Text>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666666" />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Data preview */}
          <View style={styles.dataContainer}>
            <Text style={styles.dataLabel}>Data:</Text>
            <Text style={styles.dataText}>
              {formatDataForDisplay(scanResult.data, scanResult.type)}
            </Text>
          </View>

          {/* Metadata */}
          <View style={styles.metadataContainer}>
            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Tipe:</Text>
              <Text style={styles.metadataValue}>{scanResult.type.toUpperCase()}</Text>
            </View>
            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Format:</Text>
              <Text style={styles.metadataValue}>{scanResult.format}</Text>
            </View>
            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Waktu:</Text>
              <Text style={styles.metadataValue}>
                {scanResult.timestamp.toLocaleString('id-ID')}
              </Text>
            </View>
            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Metode:</Text>
              <Text style={styles.metadataValue}>
                {scanResult.scanMethod === 'camera' ? 'Kamera' : 'Galeri'}
              </Text>
            </View>
          </View>

          {/* Raw data (collapsible) */}
          <View style={styles.rawDataContainer}>
            <Text style={styles.rawDataLabel}>Data Lengkap:</Text>
            <ScrollView style={styles.rawDataScroll} nestedScrollEnabled>
              <Text style={styles.rawDataText} selectable>
                {scanResult.data}
              </Text>
            </ScrollView>
          </View>
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={handlePrimaryAction}
          >
            <Ionicons name={getTypeIcon()} size={20} color="#FFFFFF" />
            <Text style={styles.primaryButtonText}>{getPrimaryActionLabel()}</Text>
          </TouchableOpacity>

          <View style={styles.secondaryActions}>
            <TouchableOpacity style={styles.secondaryButton} onPress={copyToClipboard}>
              <Ionicons name="copy" size={20} color="#007AFF" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.secondaryButton} onPress={showShare}>
              <Ionicons name="share" size={20} color="#007AFF" />
            </TouchableOpacity>

            {onFavorite && (
              <TouchableOpacity style={styles.secondaryButton} onPress={onFavorite}>
                <Ionicons
                  name={scanResult.isFavorite ? "heart" : "heart-outline"}
                  size={20}
                  color={scanResult.isFavorite ? "#FF3B30" : "#007AFF"}
                />
              </TouchableOpacity>
            )}

            {onSave && (
              <TouchableOpacity style={styles.secondaryButton} onPress={onSave}>
                <Ionicons name="save" size={20} color="#007AFF" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Share Modal */}
      <ShareModal
        visible={showShareModal}
        scanResult={scanResult}
        onClose={() => setShowShareModal(false)}
        onOptionSelected={(option) => {
          console.log('Share option selected:', option);
        }}
      />
    </View>
  );
}

/**
 * Styles untuk komponen ScanResult
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginLeft: 8,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    maxHeight: 400,
  },
  dataContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  dataLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 8,
  },
  dataText: {
    fontSize: 16,
    color: '#000000',
    lineHeight: 24,
  },
  metadataContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metadataLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  metadataValue: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  rawDataContainer: {
    padding: 16,
  },
  rawDataLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 8,
  },
  rawDataScroll: {
    maxHeight: 100,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
  },
  rawDataText: {
    fontSize: 12,
    color: '#333333',
    fontFamily: 'monospace',
    lineHeight: 16,
  },
  actions: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E7',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  secondaryButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
});
