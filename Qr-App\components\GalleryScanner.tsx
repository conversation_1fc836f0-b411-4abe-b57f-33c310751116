/**
 * Komponen untuk scanning QR Code dari gambar di gallery
 * Mendukung pemilihan gambar dari gallery dan batch processing
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
  Image,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Ionicons } from '@expo/vector-icons';
import { ScanResult, ScanMethod, BarcodeFormat } from '../types';
import { detectScanType, generateScanId } from '../utils/qrUtils';

const { width } = Dimensions.get('window');

interface GalleryScannerProps {
  onScanSuccess: (results: ScanResult[]) => void;
  onError?: (error: string) => void;
  allowMultiple?: boolean;
  maxImages?: number;
}

interface SelectedImage {
  uri: string;
  id: string;
  scanResult?: ScanResult;
  isProcessing: boolean;
  error?: string;
}

/**
 * Komponen untuk scanning QR Code dari gallery
 */
export default function GalleryScanner({
  onScanSuccess,
  onError,
  allowMultiple = true,
  maxImages = 10
}: GalleryScannerProps) {
  const [selectedImages, setSelectedImages] = useState<SelectedImage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * Request permission untuk akses media library
   */
  const requestPermission = async (): Promise<boolean> => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Aplikasi memerlukan akses ke gallery untuk memilih gambar'
      );
      return false;
    }
    return true;
  };

  /**
   * Pilih gambar dari gallery
   */
  const pickImages = async () => {
    try {
      const hasPermission = await requestPermission();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: allowMultiple,
        quality: 1,
        allowsEditing: false,
        selectionLimit: maxImages,
      });

      if (!result.canceled && result.assets) {
        const newImages: SelectedImage[] = result.assets.map((asset, index) => ({
          uri: asset.uri,
          id: `image_${Date.now()}_${index}`,
          isProcessing: false
        }));

        setSelectedImages(prev => [...prev, ...newImages]);
      }
    } catch (error) {
      console.error('Error picking images:', error);
      onError?.('Gagal memilih gambar dari gallery');
    }
  };

  /**
   * Scan QR code dari gambar menggunakan BarCodeScanner
   */
  const scanImageForQR = async (imageUri: string): Promise<ScanResult | null> => {
    try {
      // Note: expo-barcode-scanner tidak memiliki method untuk scan dari file
      // Untuk implementasi lengkap, perlu menggunakan library seperti:
      // - react-native-vision-camera dengan ML Kit
      // - atau service API untuk QR detection

      // Simulasi scanning (untuk demo)
      // Dalam implementasi nyata, gunakan ML Kit atau API service
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Placeholder - return null jika tidak ada QR code ditemukan
      return null;

    } catch (error) {
      console.error('Error scanning image:', error);
      throw new Error('Gagal memproses gambar');
    }
  };

  /**
   * Process semua gambar yang dipilih
   */
  const processImages = async () => {
    if (selectedImages.length === 0) {
      Alert.alert('Tidak ada gambar', 'Pilih gambar terlebih dahulu');
      return;
    }

    setIsProcessing(true);
    const results: ScanResult[] = [];

    try {
      // Process setiap gambar
      for (let i = 0; i < selectedImages.length; i++) {
        const image = selectedImages[i];

        // Update status processing
        setSelectedImages(prev => prev.map(img =>
          img.id === image.id
            ? { ...img, isProcessing: true, error: undefined }
            : img
        ));

        try {
          const scanResult = await scanImageForQR(image.uri);

          if (scanResult) {
            results.push(scanResult);

            // Update dengan hasil scan
            setSelectedImages(prev => prev.map(img =>
              img.id === image.id
                ? { ...img, isProcessing: false, scanResult }
                : img
            ));
          } else {
            // Tidak ada QR code ditemukan
            setSelectedImages(prev => prev.map(img =>
              img.id === image.id
                ? { ...img, isProcessing: false, error: 'Tidak ada QR code ditemukan' }
                : img
            ));
          }
        } catch (error) {
          // Error processing gambar
          setSelectedImages(prev => prev.map(img =>
            img.id === image.id
              ? { ...img, isProcessing: false, error: 'Gagal memproses gambar' }
              : img
          ));
        }
      }

      if (results.length > 0) {
        onScanSuccess(results);
      } else {
        Alert.alert(
          'Tidak ada QR Code',
          'Tidak ditemukan QR code pada gambar yang dipilih'
        );
      }

    } catch (error) {
      console.error('Error processing images:', error);
      onError?.('Gagal memproses gambar');
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * Hapus gambar dari daftar
   */
  const removeImage = (imageId: string) => {
    setSelectedImages(prev => prev.filter(img => img.id !== imageId));
  };

  /**
   * Clear semua gambar
   */
  const clearAllImages = () => {
    setSelectedImages([]);
  };

  /**
   * Render item gambar
   */
  const renderImageItem = ({ item }: { item: SelectedImage }) => (
    <View style={styles.imageItem}>
      <Image source={{ uri: item.uri }} style={styles.imagePreview} />

      {/* Overlay untuk status */}
      <View style={styles.imageOverlay}>
        {item.isProcessing && (
          <View style={styles.processingOverlay}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={styles.processingText}>Processing...</Text>
          </View>
        )}

        {item.scanResult && (
          <View style={styles.successOverlay}>
            <Ionicons name="checkmark-circle" size={24} color="#00FF00" />
          </View>
        )}

        {item.error && (
          <View style={styles.errorOverlay}>
            <Ionicons name="close-circle" size={24} color="#FF0000" />
          </View>
        )}
      </View>

      {/* Remove button */}
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeImage(item.id)}
      >
        <Ionicons name="close" size={16} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Scan dari Gallery</Text>
        <Text style={styles.subtitle}>
          Pilih gambar yang mengandung QR Code atau Barcode
        </Text>
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.button, styles.pickButton]}
          onPress={pickImages}
          disabled={isProcessing}
        >
          <Ionicons name="images" size={20} color="#FFFFFF" />
          <Text style={styles.buttonText}>
            {allowMultiple ? 'Pilih Gambar' : 'Pilih Gambar'}
          </Text>
        </TouchableOpacity>

        {selectedImages.length > 0 && (
          <>
            <TouchableOpacity
              style={[styles.button, styles.processButton]}
              onPress={processImages}
              disabled={isProcessing}
            >
              <Ionicons name="scan" size={20} color="#FFFFFF" />
              <Text style={styles.buttonText}>
                {isProcessing ? 'Processing...' : 'Scan Gambar'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.clearButton]}
              onPress={clearAllImages}
              disabled={isProcessing}
            >
              <Ionicons name="trash" size={20} color="#FFFFFF" />
              <Text style={styles.buttonText}>Clear All</Text>
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* Selected images */}
      {selectedImages.length > 0 && (
        <View style={styles.imagesContainer}>
          <Text style={styles.imagesTitle}>
            Gambar Dipilih ({selectedImages.length})
          </Text>
          <FlatList
            data={selectedImages}
            renderItem={renderImageItem}
            keyExtractor={item => item.id}
            numColumns={2}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.imagesList}
          />
        </View>
      )}

      {/* Empty state */}
      {selectedImages.length === 0 && (
        <View style={styles.emptyState}>
          <Ionicons name="images-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyText}>
            Belum ada gambar dipilih
          </Text>
          <Text style={styles.emptySubtext}>
            Tap "Pilih Gambar" untuk memulai
          </Text>
        </View>
      )}
    </View>
  );
}

/**
 * Styles untuk komponen GalleryScanner
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
  },
  pickButton: {
    backgroundColor: '#007AFF',
  },
  processButton: {
    backgroundColor: '#34C759',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  imagesContainer: {
    flex: 1,
  },
  imagesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  imagesList: {
    paddingBottom: 20,
  },
  imageItem: {
    width: (width - 48) / 2,
    aspectRatio: 1,
    marginRight: 16,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
    position: 'relative',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingOverlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  processingText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
  },
  successOverlay: {
    backgroundColor: 'rgba(0, 255, 0, 0.2)',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorOverlay: {
    backgroundColor: 'rgba(255, 0, 0, 0.2)',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },
});
