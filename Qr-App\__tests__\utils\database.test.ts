/**
 * Unit tests untuk Database Manager
 * Testing operasi CRUD dan manajemen database
 */

import { DatabaseManager } from '../../utils/database';
import { ScanResult, ScanType, BarcodeFormat, ScanMethod } from '../../types';

// Mock expo-sqlite
jest.mock('expo-sqlite', () => ({
  openDatabaseAsync: jest.fn(() => Promise.resolve({
    execAsync: jest.fn(() => Promise.resolve()),
    runAsync: jest.fn(() => Promise.resolve()),
    getAllAsync: jest.fn(() => Promise.resolve([])),
    getFirstAsync: jest.fn(() => Promise.resolve(null)),
    closeAsync: jest.fn(() => Promise.resolve())
  }))
}));

describe('DatabaseManager', () => {
  let databaseManager: DatabaseManager;
  let mockDb: any;

  beforeEach(() => {
    databaseManager = new DatabaseManager();
    mockDb = {
      execAsync: jest.fn(() => Promise.resolve()),
      runAsync: jest.fn(() => Promise.resolve()),
      getAllAsync: jest.fn(() => Promise.resolve([])),
      getFirstAsync: jest.fn(() => Promise.resolve(null)),
      closeAsync: jest.fn(() => Promise.resolve())
    };
    
    // Mock the database instance
    (databaseManager as any).db = mockDb;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialize', () => {
    test('should initialize database and create tables', async () => {
      await databaseManager.initialize();
      
      expect(mockDb.execAsync).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS scan_history')
      );
      expect(mockDb.execAsync).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS app_settings')
      );
      expect(mockDb.execAsync).toHaveBeenCalledWith(
        expect.stringContaining('CREATE INDEX IF NOT EXISTS')
      );
    });
  });

  describe('saveScanResult', () => {
    test('should save scan result to database', async () => {
      const scanResult: ScanResult = {
        id: 'test-id',
        data: 'https://example.com',
        type: ScanType.URL,
        format: BarcodeFormat.QR_CODE,
        timestamp: new Date('2023-01-01T00:00:00Z'),
        scanMethod: ScanMethod.CAMERA,
        isFavorite: false
      };

      await databaseManager.saveScanResult(scanResult);

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO scan_history'),
        [
          'test-id',
          'https://example.com',
          ScanType.URL,
          BarcodeFormat.QR_CODE,
          scanResult.timestamp.getTime(),
          ScanMethod.CAMERA,
          null,
          null,
          null,
          0
        ]
      );
    });

    test('should save scan result with location data', async () => {
      const scanResult: ScanResult = {
        id: 'test-id',
        data: 'test data',
        type: ScanType.TEXT,
        format: BarcodeFormat.QR_CODE,
        timestamp: new Date(),
        scanMethod: ScanMethod.CAMERA,
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'San Francisco, CA'
        },
        isFavorite: true
      };

      await databaseManager.saveScanResult(scanResult);

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO scan_history'),
        expect.arrayContaining([
          37.7749,
          -122.4194,
          'San Francisco, CA',
          1
        ])
      );
    });
  });

  describe('getScanHistory', () => {
    test('should get scan history without filters', async () => {
      const mockRows = [
        {
          id: 'test-id',
          data: 'https://example.com',
          type: ScanType.URL,
          format: BarcodeFormat.QR_CODE,
          timestamp: Date.now(),
          scan_method: ScanMethod.CAMERA,
          location_latitude: null,
          location_longitude: null,
          location_address: null,
          is_favorite: 0
        }
      ];

      mockDb.getAllAsync.mockResolvedValue(mockRows);

      const result = await databaseManager.getScanHistory();

      expect(mockDb.getAllAsync).toHaveBeenCalledWith(
        'SELECT * FROM scan_history WHERE 1=1 ORDER BY timestamp DESC',
        []
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('test-id');
      expect(result[0].data).toBe('https://example.com');
    });

    test('should get scan history with filters', async () => {
      mockDb.getAllAsync.mockResolvedValue([]);

      await databaseManager.getScanHistory(
        { type: ScanType.URL, searchQuery: 'example' },
        { field: 'timestamp', direction: 'asc' },
        10,
        0
      );

      expect(mockDb.getAllAsync).toHaveBeenCalledWith(
        expect.stringContaining('WHERE 1=1 AND type = ? AND data LIKE ? ORDER BY timestamp ASC LIMIT ? OFFSET ?'),
        [ScanType.URL, '%example%', 10, 0]
      );
    });
  });

  describe('deleteScanResult', () => {
    test('should delete scan result by id', async () => {
      await databaseManager.deleteScanResult('test-id');

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        'DELETE FROM scan_history WHERE id = ?',
        ['test-id']
      );
    });
  });

  describe('deleteScanResults', () => {
    test('should delete multiple scan results', async () => {
      const ids = ['id1', 'id2', 'id3'];
      
      await databaseManager.deleteScanResults(ids);

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        'DELETE FROM scan_history WHERE id IN (?,?,?)',
        ids
      );
    });
  });

  describe('toggleFavorite', () => {
    test('should toggle favorite status', async () => {
      await databaseManager.toggleFavorite('test-id');

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        'UPDATE scan_history SET is_favorite = NOT is_favorite WHERE id = ?',
        ['test-id']
      );
    });
  });

  describe('cleanupOldHistory', () => {
    test('should cleanup old history entries', async () => {
      const retentionDays = 30;
      
      await databaseManager.cleanupOldHistory(retentionDays);

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        'DELETE FROM scan_history WHERE timestamp < ? AND is_favorite = 0',
        [expect.any(Number)]
      );
    });
  });

  describe('saveSetting', () => {
    test('should save app setting', async () => {
      await databaseManager.saveSetting('enableSound', true);

      expect(mockDb.runAsync).toHaveBeenCalledWith(
        'INSERT OR REPLACE INTO app_settings (key, value) VALUES (?, ?)',
        ['enableSound', 'true']
      );
    });
  });

  describe('getSetting', () => {
    test('should get app setting', async () => {
      mockDb.getFirstAsync.mockResolvedValue({ value: 'true' });

      const result = await databaseManager.getSetting('enableSound', false);

      expect(mockDb.getFirstAsync).toHaveBeenCalledWith(
        'SELECT value FROM app_settings WHERE key = ?',
        ['enableSound']
      );
      expect(result).toBe(true);
    });

    test('should return default value when setting not found', async () => {
      mockDb.getFirstAsync.mockResolvedValue(null);

      const result = await databaseManager.getSetting('nonExistentSetting', 'default');

      expect(result).toBe('default');
    });
  });

  describe('close', () => {
    test('should close database connection', async () => {
      await databaseManager.close();

      expect(mockDb.closeAsync).toHaveBeenCalled();
    });
  });

  describe('mapRowToScanResult', () => {
    test('should map database row to ScanResult object', () => {
      const row = {
        id: 'test-id',
        data: 'https://example.com',
        type: ScanType.URL,
        format: BarcodeFormat.QR_CODE,
        timestamp: 1672531200000, // 2023-01-01T00:00:00Z
        scan_method: ScanMethod.CAMERA,
        location_latitude: 37.7749,
        location_longitude: -122.4194,
        location_address: 'San Francisco, CA',
        is_favorite: 1
      };

      const result = (databaseManager as any).mapRowToScanResult(row);

      expect(result).toEqual({
        id: 'test-id',
        data: 'https://example.com',
        type: ScanType.URL,
        format: BarcodeFormat.QR_CODE,
        timestamp: new Date(1672531200000),
        scanMethod: ScanMethod.CAMERA,
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'San Francisco, CA'
        },
        isFavorite: true
      });
    });

    test('should map row without location data', () => {
      const row = {
        id: 'test-id',
        data: 'test data',
        type: ScanType.TEXT,
        format: BarcodeFormat.QR_CODE,
        timestamp: 1672531200000,
        scan_method: ScanMethod.GALLERY,
        location_latitude: null,
        location_longitude: null,
        location_address: null,
        is_favorite: 0
      };

      const result = (databaseManager as any).mapRowToScanResult(row);

      expect(result.location).toBeUndefined();
      expect(result.isFavorite).toBe(false);
    });
  });
});
