load("@fbsource//tools/build_defs:glob_defs.bzl", "subdir_glob")
load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "get_apple_compiler_flags", "react_native_xplat_target", "rn_xplat_cxx_library")

CXX_LIBRARY_COMPILER_FLAGS = [
    "-std=c++14",
    "-Wall",
]

rn_xplat_cxx_library(
    name = "utils",
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/utils",
    ),
    compiler_flags = CXX_LIBRARY_COMPILER_FLAGS + [
        "-fexceptions",
        "-frtti",
    ],
    fbobjc_compiler_flags = get_apple_compiler_flags(),
    force_static = True,
    platforms = (ANDROID, APPLE, CXX),
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "fbsource//xplat/folly:evicting_cache_map",
        "fbsource//xplat/folly:headers_only",
        "fbsource//xplat/folly:memory",
        "fbsource//xplat/folly:molly",
        "fbsource//xplat/jsi:jsi",
        react_native_xplat_target("better:better"),
    ],
)
