/**
 * Screen untuk Gallery Scanner
 * Mengintegrasikan komponen GalleryScanner untuk scan dari gambar
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Modal,
  Alert
} from 'react-native';
import GalleryScanner from '@/components/GalleryScanner';
import ScanResult from '@/components/ScanResult';
import { ScanResult as ScanResultType } from '@/types';
import { databaseManager } from '@/utils/database';

export default function GalleryScreen() {
  const [scanResults, setScanResults] = useState<ScanResultType[]>([]);
  const [selectedScanResult, setSelectedScanResult] = useState<ScanResultType | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);

  /**
   * Handle hasil scan dari gallery
   */
  const handleScanSuccess = async (results: ScanResultType[]) => {
    try {
      // Save semua hasil scan ke database
      for (const result of results) {
        await databaseManager.saveScanResult(result);
      }

      setScanResults(results);
      
      // Tampilkan hasil pertama jika ada
      if (results.length > 0) {
        setSelectedScanResult(results[0]);
        setShowResultModal(true);
      }

      Alert.alert(
        'Scan Berhasil',
        `Berhasil memproses ${results.length} QR Code dari gambar`
      );

    } catch (error) {
      console.error('Error handling gallery scan results:', error);
      Alert.alert('Error', 'Gagal menyimpan hasil scan');
    }
  };

  /**
   * Handle error saat scanning
   */
  const handleScanError = (error: string) => {
    Alert.alert('Error Scanning', error);
  };

  /**
   * Tutup modal hasil scan
   */
  const closeResultModal = () => {
    setShowResultModal(false);
    setSelectedScanResult(null);
  };

  /**
   * Toggle favorite status
   */
  const toggleFavorite = async () => {
    if (!selectedScanResult) return;

    try {
      await databaseManager.toggleFavorite(selectedScanResult.id);
      setSelectedScanResult({
        ...selectedScanResult,
        isFavorite: !selectedScanResult.isFavorite
      });
      
      // Update hasil scan di state
      setScanResults(prev => 
        prev.map(result => 
          result.id === selectedScanResult.id 
            ? { ...result, isFavorite: !result.isFavorite }
            : result
        )
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Gagal mengubah status favorite');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Gallery Scanner */}
      <GalleryScanner
        onScanSuccess={handleScanSuccess}
        onError={handleScanError}
        allowMultiple={true}
        maxImages={10}
      />

      {/* Modal untuk menampilkan hasil scan */}
      <Modal
        visible={showResultModal}
        animationType="slide"
        transparent={true}
        onRequestClose={closeResultModal}
      >
        {selectedScanResult && (
          <ScanResult
            scanResult={selectedScanResult}
            onClose={closeResultModal}
            onFavorite={toggleFavorite}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
}

/**
 * Styles untuk GalleryScreen
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
