/**
 * Komponen modal untuk sharing options
 * Menampilkan berbagai pilihan sharing dan save untuk scan result
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ScanResult } from '../types';
import { getSharingOptions, formatDataForDisplay } from '../utils/shareUtils';

const { width } = Dimensions.get('window');

interface ShareModalProps {
  visible: boolean;
  scanResult: ScanResult | null;
  onClose: () => void;
  onOptionSelected?: (option: string) => void;
}

/**
 * Modal untuk menampilkan opsi sharing
 */
export default function ShareModal({
  visible,
  scanResult,
  onClose,
  onOptionSelected
}: ShareModalProps) {
  
  if (!scanResult) return null;

  const sharingOptions = getSharingOptions(scanResult);

  /**
   * Handle option selection
   */
  const handleOptionPress = async (option: any) => {
    try {
      await option.action();
      onOptionSelected?.(option.key);
      onClose();
    } catch (error) {
      console.error('Error executing sharing option:', error);
      Alert.alert('Error', (error as Error).message);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Share Options</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>

          {/* Data preview */}
          <View style={styles.dataPreview}>
            <View style={styles.dataTypeContainer}>
              <Ionicons 
                name={getTypeIcon(scanResult.type)} 
                size={16} 
                color="#007AFF" 
              />
              <Text style={styles.dataType}>
                {scanResult.type.toUpperCase()}
              </Text>
            </View>
            <Text style={styles.dataText} numberOfLines={3}>
              {formatDataForDisplay(scanResult.data, scanResult.type)}
            </Text>
          </View>

          {/* Sharing options */}
          <ScrollView 
            style={styles.optionsContainer}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.optionsGrid}>
              {sharingOptions.map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={styles.optionButton}
                  onPress={() => handleOptionPress(option)}
                >
                  <View style={styles.optionIconContainer}>
                    <Ionicons 
                      name={option.icon as any} 
                      size={24} 
                      color="#007AFF" 
                    />
                  </View>
                  <Text style={styles.optionLabel}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Cancel button */}
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Batal</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

/**
 * Get icon berdasarkan scan type
 */
function getTypeIcon(type: string): string {
  switch (type) {
    case 'url': return 'link';
    case 'email': return 'mail';
    case 'phone': return 'call';
    case 'sms': return 'chatbubble';
    case 'wifi': return 'wifi';
    case 'contact': return 'person';
    case 'calendar': return 'calendar';
    case 'location': return 'location';
    default: return 'document-text';
  }
}

/**
 * Styles untuk ShareModal
 */
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  closeButton: {
    padding: 4,
  },
  dataPreview: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  dataTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  dataType: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
  },
  dataText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  optionButton: {
    width: (width - 80) / 4, // 4 columns with gaps
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  optionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
  },
  cancelButton: {
    marginHorizontal: 20,
    marginTop: 16,
    paddingVertical: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
});
