# API Documentation - QR Scanner Pro

Dokumentasi lengkap untuk komponen, utilities, dan API yang tersedia dalam aplikasi QR Scanner Pro.

## 📚 Table of Contents

1. [Components](#components)
2. [Types](#types)
3. [Utilities](#utilities)
4. [Database API](#database-api)
5. [Examples](#examples)

## Components

### QRScanner

Komponen utama untuk scanning QR Code dan Barcode menggunakan kamera device.

#### Props
```typescript
interface QRScannerProps {
  onScanSuccess: (result: ScanResult) => void;
  onError?: (error: string) => void;
  enableSound?: boolean;
  enableVibration?: boolean;
  autoSave?: boolean;
}
```

#### Usage
```typescript
import QRScanner from '@/components/QRScanner';

<QRScanner
  onScanSuccess={(result) => console.log('Scan result:', result)}
  onError={(error) => console.error('Scan error:', error)}
  enableSound={true}
  enableVibration={true}
  autoSave={true}
/>
```

### GalleryScanner

Komponen untuk scanning QR Code dari gambar yang dipilih dari gallery.

#### Props
```typescript
interface GalleryScannerProps {
  onScanSuccess: (results: ScanResult[]) => void;
  onError?: (error: string) => void;
  allowMultiple?: boolean;
  maxImages?: number;
}
```

#### Usage
```typescript
import GalleryScanner from '@/components/GalleryScanner';

<GalleryScanner
  onScanSuccess={(results) => console.log('Scan results:', results)}
  onError={(error) => console.error('Scan error:', error)}
  allowMultiple={true}
  maxImages={10}
/>
```

### QRGenerator

Komponen untuk generate QR Code dengan berbagai tipe data.

#### Props
```typescript
interface QRGeneratorProps {
  onGenerated?: (data: string, type: QRType) => void;
}
```

#### Usage
```typescript
import QRGenerator from '@/components/QRGenerator';

<QRGenerator
  onGenerated={(data, type) => console.log('Generated QR:', data, type)}
/>
```

### ScanHistory

Komponen untuk menampilkan dan mengelola history scan.

#### Props
```typescript
interface ScanHistoryProps {
  onScanResultPress?: (scanResult: ScanResult) => void;
}
```

#### Usage
```typescript
import ScanHistory from '@/components/ScanHistory';

<ScanHistory
  onScanResultPress={(result) => console.log('History item pressed:', result)}
/>
```

### ScanResult

Komponen modal untuk menampilkan detail hasil scan.

#### Props
```typescript
interface ScanResultProps {
  scanResult: ScanResult;
  onClose: () => void;
  onSave?: () => void;
  onFavorite?: () => void;
}
```

#### Usage
```typescript
import ScanResult from '@/components/ScanResult';

<ScanResult
  scanResult={scanResult}
  onClose={() => setShowModal(false)}
  onSave={() => saveScanResult()}
  onFavorite={() => toggleFavorite()}
/>
```

### ShareModal

Komponen modal untuk menampilkan opsi sharing.

#### Props
```typescript
interface ShareModalProps {
  visible: boolean;
  scanResult: ScanResult | null;
  onClose: () => void;
  onOptionSelected?: (option: string) => void;
}
```

#### Usage
```typescript
import ShareModal from '@/components/ShareModal';

<ShareModal
  visible={showShareModal}
  scanResult={selectedResult}
  onClose={() => setShowShareModal(false)}
  onOptionSelected={(option) => console.log('Share option:', option)}
/>
```

## Types

### ScanResult
```typescript
interface ScanResult {
  id: string;
  data: string;
  type: ScanType;
  format: BarcodeFormat;
  timestamp: Date;
  scanMethod: ScanMethod;
  location?: LocationData;
  isFavorite: boolean;
}
```

### ScanType
```typescript
enum ScanType {
  TEXT = 'text',
  URL = 'url',
  EMAIL = 'email',
  PHONE = 'phone',
  SMS = 'sms',
  WIFI = 'wifi',
  CONTACT = 'contact',
  CALENDAR = 'calendar',
  LOCATION = 'location',
  UNKNOWN = 'unknown'
}
```

### BarcodeFormat
```typescript
enum BarcodeFormat {
  QR_CODE = 'qr_code',
  AZTEC = 'aztec',
  CODABAR = 'codabar',
  CODE_39 = 'code_39',
  CODE_93 = 'code_93',
  CODE_128 = 'code_128',
  DATA_MATRIX = 'data_matrix',
  EAN_8 = 'ean_8',
  EAN_13 = 'ean_13',
  ITF = 'itf',
  PDF_417 = 'pdf_417',
  UPC_A = 'upc_a',
  UPC_E = 'upc_e'
}
```

### QRGeneratorConfig
```typescript
interface QRGeneratorConfig {
  size: number;
  errorCorrectionLevel: ErrorCorrectionLevel;
  foregroundColor: string;
  backgroundColor: string;
  logo?: string;
  logoSize?: number;
  quietZone: number;
}
```

### ContactData
```typescript
interface ContactData {
  firstName: string;
  lastName: string;
  organization?: string;
  phone?: string;
  email?: string;
  website?: string;
  address?: string;
}
```

### WiFiData
```typescript
interface WiFiData {
  ssid: string;
  password: string;
  security: WiFiSecurity;
  hidden?: boolean;
}
```

## Utilities

### QR Utils

#### detectScanType(data: string): ScanType
Mendeteksi tipe data dari hasil scan QR/Barcode.

```typescript
import { detectScanType } from '@/utils/qrUtils';

const type = detectScanType('https://google.com');
// Returns: ScanType.URL
```

#### parseWiFiData(data: string): WiFiData | null
Parse data WiFi dari QR code.

```typescript
import { parseWiFiData } from '@/utils/qrUtils';

const wifiData = parseWiFiData('WIFI:T:WPA;S:MyNetwork;P:password123;;');
// Returns: { ssid: 'MyNetwork', password: 'password123', security: 'WPA', hidden: false }
```

#### parseVCardData(data: string): ContactData | null
Parse vCard contact data.

```typescript
import { parseVCardData } from '@/utils/qrUtils';

const contactData = parseVCardData(vCardString);
// Returns: ContactData object
```

#### generateVCardString(contact: ContactData): string
Generate vCard string dari contact data.

```typescript
import { generateVCardString } from '@/utils/qrUtils';

const vCardString = generateVCardString({
  firstName: 'John',
  lastName: 'Doe',
  phone: '+1234567890',
  email: '<EMAIL>'
});
```

#### generateWiFiQRString(wifiData: WiFiData): string
Generate WiFi QR code string.

```typescript
import { generateWiFiQRString } from '@/utils/qrUtils';

const wifiString = generateWiFiQRString({
  ssid: 'MyNetwork',
  password: 'password123',
  security: WiFiSecurity.WPA,
  hidden: false
});
```

### Share Utils

#### shareData(data: string, title?: string): Promise<void>
Share data menggunakan native share sheet.

```typescript
import { shareData } from '@/utils/shareUtils';

await shareData('Hello World', 'My QR Data');
```

#### copyToClipboard(data: string): Promise<void>
Copy data ke clipboard.

```typescript
import { copyToClipboard } from '@/utils/shareUtils';

await copyToClipboard('Text to copy');
```

#### shareViaWhatsApp(data: string): Promise<void>
Share data via WhatsApp.

```typescript
import { shareViaWhatsApp } from '@/utils/shareUtils';

await shareViaWhatsApp('Hello from QR Scanner!');
```

#### exportScanResultsAsCSV(scanResults: ScanResult[], filename?: string): Promise<void>
Export scan results sebagai CSV file.

```typescript
import { exportScanResultsAsCSV } from '@/utils/shareUtils';

await exportScanResultsAsCSV(scanResults, 'my_history.csv');
```

## Database API

### DatabaseManager

#### initialize(): Promise<void>
Inisialisasi database dan buat tabel.

```typescript
import { databaseManager } from '@/utils/database';

await databaseManager.initialize();
```

#### saveScanResult(scanResult: ScanResult): Promise<void>
Simpan hasil scan ke database.

```typescript
await databaseManager.saveScanResult(scanResult);
```

#### getScanHistory(filter?: HistoryFilter, sort?: SortOptions, limit?: number, offset?: number): Promise<ScanResult[]>
Ambil history scan dengan filter dan sorting.

```typescript
const history = await databaseManager.getScanHistory(
  { type: ScanType.URL },
  { field: 'timestamp', direction: 'desc' },
  50,
  0
);
```

#### deleteScanResult(id: string): Promise<void>
Hapus scan result berdasarkan ID.

```typescript
await databaseManager.deleteScanResult('scan_id_123');
```

#### toggleFavorite(id: string): Promise<void>
Toggle favorite status.

```typescript
await databaseManager.toggleFavorite('scan_id_123');
```

#### saveSetting(key: string, value: any): Promise<void>
Simpan pengaturan aplikasi.

```typescript
await databaseManager.saveSetting('enableSound', true);
```

#### getSetting(key: string, defaultValue?: any): Promise<any>
Ambil pengaturan aplikasi.

```typescript
const enableSound = await databaseManager.getSetting('enableSound', true);
```

## Examples

### Complete Scanning Flow
```typescript
import React, { useState } from 'react';
import { View, Modal } from 'react-native';
import QRScanner from '@/components/QRScanner';
import ScanResult from '@/components/ScanResult';
import { ScanResult as ScanResultType } from '@/types';
import { databaseManager } from '@/utils/database';

export default function ScannerExample() {
  const [scanResult, setScanResult] = useState<ScanResultType | null>(null);
  const [showResult, setShowResult] = useState(false);

  const handleScanSuccess = async (result: ScanResultType) => {
    // Auto save to database
    await databaseManager.saveScanResult(result);
    
    // Show result modal
    setScanResult(result);
    setShowResult(true);
  };

  const handleScanError = (error: string) => {
    console.error('Scan error:', error);
  };

  const toggleFavorite = async () => {
    if (scanResult) {
      await databaseManager.toggleFavorite(scanResult.id);
      setScanResult({
        ...scanResult,
        isFavorite: !scanResult.isFavorite
      });
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <QRScanner
        onScanSuccess={handleScanSuccess}
        onError={handleScanError}
        enableSound={true}
        enableVibration={true}
        autoSave={true}
      />

      <Modal visible={showResult} transparent>
        {scanResult && (
          <ScanResult
            scanResult={scanResult}
            onClose={() => setShowResult(false)}
            onFavorite={toggleFavorite}
          />
        )}
      </Modal>
    </View>
  );
}
```

### QR Code Generation Example
```typescript
import React from 'react';
import { View } from 'react-native';
import QRGenerator from '@/components/QRGenerator';
import { databaseManager } from '@/utils/database';
import { generateScanId, detectScanType } from '@/utils/qrUtils';

export default function GeneratorExample() {
  const handleQRGenerated = async (data: string, type: string) => {
    // Save generated QR to history
    const scanResult = {
      id: generateScanId(),
      data,
      type: detectScanType(data),
      format: 'qr_code',
      timestamp: new Date(),
      scanMethod: 'camera',
      isFavorite: false
    };

    await databaseManager.saveScanResult(scanResult);
    console.log('QR code generated and saved:', data);
  };

  return (
    <View style={{ flex: 1 }}>
      <QRGenerator onGenerated={handleQRGenerated} />
    </View>
  );
}
```
