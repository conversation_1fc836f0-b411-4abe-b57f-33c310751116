/**
 * Komponen untuk menampilkan dan mengelola history scan
 * Menyediakan fitur search, filter, sort, delete, dan export
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  Share
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ScanResult, HistoryFilter, SortOptions, ScanType } from '../types';
import { databaseManager } from '../utils/database';
import { formatDataForDisplay } from '../utils/qrUtils';

interface ScanHistoryProps {
  onScanResultPress?: (scanResult: ScanResult) => void;
}

/**
 * Komponen untuk mengelola history scan
 */
export default function ScanHistory({ onScanResultPress }: ScanHistoryProps) {
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<ScanResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<HistoryFilter>({});
  const [currentSort, setCurrentSort] = useState<SortOptions>({
    field: 'timestamp',
    direction: 'desc'
  });

  /**
   * Load history saat komponen dimount
   */
  useEffect(() => {
    loadHistory();
  }, [currentFilter, currentSort]);

  /**
   * Filter history berdasarkan search query
   */
  useEffect(() => {
    filterHistory();
  }, [scanHistory, searchQuery]);

  /**
   * Load scan history dari database
   */
  const loadHistory = async () => {
    try {
      setIsLoading(true);
      const history = await databaseManager.getScanHistory(
        currentFilter,
        currentSort,
        100 // Limit 100 items
      );
      setScanHistory(history);
    } catch (error) {
      console.error('Error loading history:', error);
      Alert.alert('Error', 'Gagal memuat history scan');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh history
   */
  const refreshHistory = async () => {
    setIsRefreshing(true);
    await loadHistory();
    setIsRefreshing(false);
  };

  /**
   * Filter history berdasarkan search query
   */
  const filterHistory = () => {
    if (!searchQuery.trim()) {
      setFilteredHistory(scanHistory);
      return;
    }

    const filtered = scanHistory.filter(item =>
      item.data.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.type.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredHistory(filtered);
  };

  /**
   * Toggle selection mode
   */
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedItems(new Set());
  };

  /**
   * Toggle item selection
   */
  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
    } else {
      newSelection.add(itemId);
    }
    setSelectedItems(newSelection);
  };

  /**
   * Select all items
   */
  const selectAllItems = () => {
    const allIds = new Set(filteredHistory.map(item => item.id));
    setSelectedItems(allIds);
  };

  /**
   * Clear selection
   */
  const clearSelection = () => {
    setSelectedItems(new Set());
  };

  /**
   * Delete selected items
   */
  const deleteSelectedItems = async () => {
    if (selectedItems.size === 0) return;

    Alert.alert(
      'Konfirmasi Hapus',
      `Hapus ${selectedItems.size} item yang dipilih?`,
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: async () => {
            try {
              await databaseManager.deleteScanResults(Array.from(selectedItems));
              await loadHistory();
              setSelectedItems(new Set());
              setIsSelectionMode(false);
              Alert.alert('Berhasil', 'Item berhasil dihapus');
            } catch (error) {
              console.error('Error deleting items:', error);
              Alert.alert('Error', 'Gagal menghapus item');
            }
          }
        }
      ]
    );
  };

  /**
   * Export history sebagai CSV
   */
  const exportHistory = async () => {
    try {
      const dataToExport = selectedItems.size > 0
        ? filteredHistory.filter(item => selectedItems.has(item.id))
        : filteredHistory;

      if (dataToExport.length === 0) {
        Alert.alert('Tidak ada data', 'Tidak ada data untuk diekspor');
        return;
      }

      // Generate CSV content
      const csvHeader = 'Timestamp,Type,Format,Data,Method,Favorite\n';
      const csvContent = dataToExport.map(item =>
        `"${item.timestamp.toISOString()}","${item.type}","${item.format}","${item.data.replace(/"/g, '""')}","${item.scanMethod}","${item.isFavorite}"`
      ).join('\n');

      const csvData = csvHeader + csvContent;

      // Share CSV data
      await Share.share({
        message: csvData,
        title: 'QR Scanner History Export'
      });

    } catch (error) {
      console.error('Error exporting history:', error);
      Alert.alert('Error', 'Gagal mengekspor data');
    }
  };

  /**
   * Toggle favorite status
   */
  const toggleFavorite = async (item: ScanResult) => {
    try {
      await databaseManager.toggleFavorite(item.id);
      await loadHistory();
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Gagal mengubah status favorite');
    }
  };

  /**
   * Get icon berdasarkan scan type
   */
  const getTypeIcon = (type: ScanType): string => {
    switch (type) {
      case ScanType.URL: return 'link';
      case ScanType.EMAIL: return 'mail';
      case ScanType.PHONE: return 'call';
      case ScanType.SMS: return 'chatbubble';
      case ScanType.WIFI: return 'wifi';
      case ScanType.CONTACT: return 'person';
      case ScanType.CALENDAR: return 'calendar';
      case ScanType.LOCATION: return 'location';
      default: return 'document-text';
    }
  };

  /**
   * Render item history
   */
  const renderHistoryItem = ({ item }: { item: ScanResult }) => (
    <TouchableOpacity
      style={[
        styles.historyItem,
        selectedItems.has(item.id) && styles.selectedItem
      ]}
      onPress={() => {
        if (isSelectionMode) {
          toggleItemSelection(item.id);
        } else {
          onScanResultPress?.(item);
        }
      }}
      onLongPress={() => {
        if (!isSelectionMode) {
          setIsSelectionMode(true);
          toggleItemSelection(item.id);
        }
      }}
    >
      {/* Selection checkbox */}
      {isSelectionMode && (
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => toggleItemSelection(item.id)}
        >
          <Ionicons
            name={selectedItems.has(item.id) ? 'checkbox' : 'square-outline'}
            size={24}
            color={selectedItems.has(item.id) ? '#007AFF' : '#CCCCCC'}
          />
        </TouchableOpacity>
      )}

      {/* Content */}
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <View style={styles.itemTypeContainer}>
            <Ionicons
              name={getTypeIcon(item.type) as any}
              size={16}
              color="#007AFF"
            />
            <Text style={styles.itemType}>{item.type.toUpperCase()}</Text>
          </View>
          <Text style={styles.itemTimestamp}>
            {item.timestamp.toLocaleDateString('id-ID')}
          </Text>
        </View>

        <Text style={styles.itemData} numberOfLines={2}>
          {formatDataForDisplay(item.data, item.type)}
        </Text>

        <View style={styles.itemFooter}>
          <Text style={styles.itemMethod}>
            {item.scanMethod === 'camera' ? 'Kamera' : 'Galeri'}
          </Text>
          <TouchableOpacity
            onPress={() => toggleFavorite(item)}
            style={styles.favoriteButton}
          >
            <Ionicons
              name={item.isFavorite ? 'heart' : 'heart-outline'}
              size={16}
              color={item.isFavorite ? '#FF3B30' : '#CCCCCC'}
            />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>History Scan</Text>

        {/* Action buttons */}
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={toggleSelectionMode}
          >
            <Ionicons
              name={isSelectionMode ? 'close' : 'checkmark-circle-outline'}
              size={24}
              color="#007AFF"
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilterModal(true)}
          >
            <Ionicons name="filter" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666666" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cari dalam history..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Selection toolbar */}
      {isSelectionMode && (
        <View style={styles.selectionToolbar}>
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={selectAllItems}
          >
            <Text style={styles.toolbarButtonText}>Pilih Semua</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={clearSelection}
          >
            <Text style={styles.toolbarButtonText}>Batal Pilih</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.toolbarButton, styles.deleteButton]}
            onPress={deleteSelectedItems}
            disabled={selectedItems.size === 0}
          >
            <Text style={[
              styles.toolbarButtonText,
              selectedItems.size === 0 && styles.disabledText
            ]}>
              Hapus ({selectedItems.size})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={exportHistory}
          >
            <Text style={styles.toolbarButtonText}>Export</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* History list */}
      <FlatList
        data={filteredHistory}
        renderItem={renderHistoryItem}
        keyExtractor={item => item.id}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refreshHistory}
            colors={['#007AFF']}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="time-outline" size={64} color="#CCCCCC" />
            <Text style={styles.emptyText}>
              {searchQuery ? 'Tidak ada hasil pencarian' : 'Belum ada history scan'}
            </Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Coba kata kunci lain' : 'Mulai scan QR code untuk melihat history'}
            </Text>
          </View>
        }
      />
    </View>
  );
}

/**
 * Styles untuk komponen ScanHistory
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  headerButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
    paddingVertical: 4,
  },
  selectionToolbar: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
    gap: 12,
  },
  toolbarButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
  },
  toolbarButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  disabledText: {
    color: '#CCCCCC',
  },
  listContainer: {
    paddingBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    alignItems: 'center',
  },
  selectedItem: {
    backgroundColor: '#E3F2FD',
  },
  checkbox: {
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  itemType: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
  },
  itemTimestamp: {
    fontSize: 12,
    color: '#666666',
  },
  itemData: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 20,
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemMethod: {
    fontSize: 12,
    color: '#666666',
  },
  favoriteButton: {
    padding: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 20,
  },
});
