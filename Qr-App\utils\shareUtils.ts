/**
 * Utility functions untuk sharing dan saving functionality
 * Menangani berbagai metode sharing dan penyimpanan data
 */

import { Alert, Linking, Share } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { ScanResult, ScanType } from '../types';
import { formatDataForDisplay } from './qrUtils';

/**
 * Share data menggunakan native share sheet
 */
export async function shareData(data: string, title?: string): Promise<void> {
  try {
    await Share.share({
      message: data,
      title: title || 'QR Scanner Data'
    });
  } catch (error) {
    console.error('Error sharing data:', error);
    throw new Error('Gagal share data');
  }
}

/**
 * Copy data ke clipboard
 */
export async function copyToClipboard(data: string): Promise<void> {
  try {
    await Clipboard.setStringAsync(data);
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    throw new Error('Gagal menyalin data');
  }
}

/**
 * Share via email
 */
export async function shareViaEmail(
  data: string, 
  subject?: string, 
  body?: string
): Promise<void> {
  try {
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject || 'QR Scanner Data')}&body=${encodeURIComponent(body || data)}`;
    const canOpen = await Linking.canOpenURL(emailUrl);
    
    if (canOpen) {
      await Linking.openURL(emailUrl);
    } else {
      throw new Error('Tidak dapat membuka aplikasi email');
    }
  } catch (error) {
    console.error('Error sharing via email:', error);
    throw new Error('Gagal share via email');
  }
}

/**
 * Share via SMS
 */
export async function shareViaSMS(data: string, phoneNumber?: string): Promise<void> {
  try {
    const smsUrl = phoneNumber 
      ? `sms:${phoneNumber}?body=${encodeURIComponent(data)}`
      : `sms:?body=${encodeURIComponent(data)}`;
    
    const canOpen = await Linking.canOpenURL(smsUrl);
    
    if (canOpen) {
      await Linking.openURL(smsUrl);
    } else {
      throw new Error('Tidak dapat membuka aplikasi SMS');
    }
  } catch (error) {
    console.error('Error sharing via SMS:', error);
    throw new Error('Gagal share via SMS');
  }
}

/**
 * Share via WhatsApp
 */
export async function shareViaWhatsApp(data: string): Promise<void> {
  try {
    const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(data)}`;
    const canOpen = await Linking.canOpenURL(whatsappUrl);
    
    if (canOpen) {
      await Linking.openURL(whatsappUrl);
    } else {
      // Fallback ke web WhatsApp
      const webWhatsappUrl = `https://wa.me/?text=${encodeURIComponent(data)}`;
      await Linking.openURL(webWhatsappUrl);
    }
  } catch (error) {
    console.error('Error sharing via WhatsApp:', error);
    throw new Error('Gagal share via WhatsApp');
  }
}

/**
 * Share via Telegram
 */
export async function shareViaTelegram(data: string): Promise<void> {
  try {
    const telegramUrl = `tg://msg?text=${encodeURIComponent(data)}`;
    const canOpen = await Linking.canOpenURL(telegramUrl);
    
    if (canOpen) {
      await Linking.openURL(telegramUrl);
    } else {
      // Fallback ke web Telegram
      const webTelegramUrl = `https://t.me/share/url?url=${encodeURIComponent(data)}`;
      await Linking.openURL(webTelegramUrl);
    }
  } catch (error) {
    console.error('Error sharing via Telegram:', error);
    throw new Error('Gagal share via Telegram');
  }
}

/**
 * Save text data sebagai file
 */
export async function saveAsTextFile(
  data: string, 
  filename?: string
): Promise<void> {
  try {
    const fileName = filename || `qr_data_${Date.now()}.txt`;
    const fileUri = `${FileSystem.documentDirectory}${fileName}`;
    
    await FileSystem.writeAsStringAsync(fileUri, data);
    
    // Share file
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(fileUri);
    } else {
      throw new Error('Sharing tidak tersedia');
    }
  } catch (error) {
    console.error('Error saving as text file:', error);
    throw new Error('Gagal menyimpan sebagai file');
  }
}

/**
 * Export scan results sebagai CSV
 */
export async function exportScanResultsAsCSV(
  scanResults: ScanResult[],
  filename?: string
): Promise<void> {
  try {
    if (scanResults.length === 0) {
      throw new Error('Tidak ada data untuk diekspor');
    }

    const fileName = filename || `qr_history_${Date.now()}.csv`;
    
    // Generate CSV content
    const csvHeader = 'Timestamp,Type,Format,Data,Method,Favorite\n';
    const csvContent = scanResults.map(item => 
      `"${item.timestamp.toISOString()}","${item.type}","${item.format}","${item.data.replace(/"/g, '""')}","${item.scanMethod}","${item.isFavorite}"`
    ).join('\n');

    const csvData = csvHeader + csvContent;
    const fileUri = `${FileSystem.documentDirectory}${fileName}`;
    
    await FileSystem.writeAsStringAsync(fileUri, csvData);
    
    // Share CSV file
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(fileUri);
    } else {
      throw new Error('Sharing tidak tersedia');
    }
  } catch (error) {
    console.error('Error exporting as CSV:', error);
    throw new Error('Gagal mengekspor sebagai CSV');
  }
}

/**
 * Export scan results sebagai JSON
 */
export async function exportScanResultsAsJSON(
  scanResults: ScanResult[],
  filename?: string
): Promise<void> {
  try {
    if (scanResults.length === 0) {
      throw new Error('Tidak ada data untuk diekspor');
    }

    const fileName = filename || `qr_history_${Date.now()}.json`;
    const jsonData = JSON.stringify(scanResults, null, 2);
    const fileUri = `${FileSystem.documentDirectory}${fileName}`;
    
    await FileSystem.writeAsStringAsync(fileUri, jsonData);
    
    // Share JSON file
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(fileUri);
    } else {
      throw new Error('Sharing tidak tersedia');
    }
  } catch (error) {
    console.error('Error exporting as JSON:', error);
    throw new Error('Gagal mengekspor sebagai JSON');
  }
}

/**
 * Save image ke gallery
 */
export async function saveImageToGallery(
  imageUri: string,
  albumName?: string
): Promise<void> {
  try {
    // Request permission
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      throw new Error('Permission tidak diberikan untuk akses gallery');
    }

    // Save to gallery
    const asset = await MediaLibrary.saveToLibraryAsync(imageUri);
    
    // Create album if specified
    if (albumName) {
      try {
        const album = await MediaLibrary.getAlbumAsync(albumName);
        if (album) {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        } else {
          await MediaLibrary.createAlbumAsync(albumName, asset, false);
        }
      } catch (albumError) {
        console.warn('Could not create/add to album:', albumError);
        // Image is still saved to gallery, just not in specific album
      }
    }
  } catch (error) {
    console.error('Error saving image to gallery:', error);
    throw new Error('Gagal menyimpan gambar ke gallery');
  }
}

/**
 * Get sharing options berdasarkan tipe data
 */
export function getSharingOptions(scanResult: ScanResult): Array<{
  key: string;
  label: string;
  icon: string;
  action: () => Promise<void>;
}> {
  const options = [
    {
      key: 'copy',
      label: 'Salin',
      icon: 'copy',
      action: () => copyToClipboard(scanResult.data)
    },
    {
      key: 'share',
      label: 'Share',
      icon: 'share',
      action: () => shareData(scanResult.data, `QR ${scanResult.type.toUpperCase()}`)
    },
    {
      key: 'whatsapp',
      label: 'WhatsApp',
      icon: 'logo-whatsapp',
      action: () => shareViaWhatsApp(scanResult.data)
    },
    {
      key: 'telegram',
      label: 'Telegram',
      icon: 'paper-plane',
      action: () => shareViaTelegram(scanResult.data)
    },
    {
      key: 'email',
      label: 'Email',
      icon: 'mail',
      action: () => shareViaEmail(
        scanResult.data,
        `QR ${scanResult.type.toUpperCase()}`,
        `Data dari QR Scanner:\n\n${scanResult.data}`
      )
    },
    {
      key: 'sms',
      label: 'SMS',
      icon: 'chatbubble',
      action: () => shareViaSMS(scanResult.data)
    },
    {
      key: 'file',
      label: 'Save File',
      icon: 'document',
      action: () => saveAsTextFile(
        scanResult.data,
        `qr_${scanResult.type}_${Date.now()}.txt`
      )
    }
  ];

  // Add specific options based on data type
  if (scanResult.type === ScanType.URL) {
    options.unshift({
      key: 'open',
      label: 'Buka URL',
      icon: 'open',
      action: async () => {
        const canOpen = await Linking.canOpenURL(scanResult.data);
        if (canOpen) {
          await Linking.openURL(scanResult.data);
        } else {
          throw new Error('Tidak dapat membuka URL');
        }
      }
    });
  }

  return options;
}

/**
 * Show sharing options dalam action sheet
 */
export function showSharingOptions(
  scanResult: ScanResult,
  onOptionSelected?: (option: string) => void
): void {
  const options = getSharingOptions(scanResult);
  const optionLabels = options.map(opt => opt.label);
  optionLabels.push('Batal');

  Alert.alert(
    'Pilih Aksi',
    formatDataForDisplay(scanResult.data, scanResult.type),
    [
      ...options.map((option, index) => ({
        text: option.label,
        onPress: async () => {
          try {
            await option.action();
            onOptionSelected?.(option.key);
          } catch (error) {
            Alert.alert('Error', (error as Error).message);
          }
        }
      })),
      {
        text: 'Batal',
        style: 'cancel' as const
      }
    ]
  );
}
