# QR Scanner Pro - Aplikasi Scanner QR Code dan Barcode

Aplikasi mobile untuk scanning dan generating QR Code serta Barcode dengan fitur lengkap, dikembangkan menggunakan React Native dan Expo.

## 📱 Fitur Utama

### Phase 1 MVP (Sudah Diimplementasi)

#### 🔍 QR Code dan Barcode Scanner Engine
- **Real-time Camera Scanning**
  - Auto-focus dengan tap-to-focus override
  - Zoom functionality (pinch-to-zoom)
  - Flashlight toggle untuk kondisi cahaya rendah
  - Support orientasi portrait/landscape
  - Frame rate optimization (30 FPS minimum)

- **Format yang Didukung**
  - QR Codes: Standard QR, Micro QR
  - 1D Barcodes: UPC-A, UPC-E, EAN-8, EAN-13, Code 39, Code 93, Code 128, ITF, Codabar
  - 2D Barcodes: Data Matrix, PDF417, Aztec

- **Image Gallery Scanning**
  - Pilih gambar dari device gallery
  - Support format JPEG, PNG, WebP
  - Batch processing untuk multiple images
  - Image enhancement untuk recognition yang lebih baik

- **Feedback Mechanisms**
  - Audio beep saat berhasil scan (dapat dikonfigurasi)
  - Haptic vibration feedback (dapat dikonfigurasi)
  - Visual confirmation dengan green overlay
  - Error handling dengan pesan user-friendly

#### 📊 Scan History Management
- **Local SQLite Database**
  - Encrypted storage untuk data sensitif
  - Automatic cleanup untuk entries lama
  - Backup/restore functionality

- **History Features**
  - Timestamp (tanggal dan waktu)
  - Content type identification (URL, text, contact, WiFi, dll)
  - Raw data content
  - Scan method (camera vs gallery)
  - Location data (opsional, dengan user permission)
  - Search functionality dengan filters
  - Sort berdasarkan date, type, atau content
  - Bulk selection dan deletion
  - Export history sebagai CSV/JSON
  - Favorite/bookmark untuk scan penting

#### 🎯 QR Code Generation
- **Generation Types**
  - Text QR Codes dengan character limit display
  - URL QR Codes dengan validation dan preview
  - Contact Information (vCard) dengan import dari device contacts
  - WiFi Credentials untuk easy network sharing

- **Customization Options**
  - Size options: Small (256px), Medium (512px), Large (1024px), Custom
  - Error correction levels: L (7%), M (15%), Q (25%), H (30%)
  - Color customization: Foreground dan background colors
  - Logo embedding dengan size adjustment
  - Border options: Configurable quiet zone

#### 📤 Share dan Save Functionality
- **Sharing Options**
  - Social Media Integration: WhatsApp, Telegram, Facebook, Twitter
  - Communication Apps: Email, SMS/MMS, Bluetooth
  - Native share sheet untuk berbagai aplikasi

- **Save Operations**
  - Gallery integration dengan high-resolution PNG/JPEG export
  - Automatic album creation
  - Metadata preservation
  - Cloud storage integration (Google Drive, Dropbox, OneDrive)

## 🚀 Instalasi dan Setup

### Prerequisites
- Node.js (v16 atau lebih baru)
- npm atau yarn
- Expo CLI
- Android Studio (untuk Android development)
- Xcode (untuk iOS development, hanya di macOS)

### Langkah Instalasi

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm start
   ```

3. **Run di device/emulator**
   ```bash
   # Android
   npm run android

   # iOS
   npm run ios

   # Web
   npm run web
   ```

## 📖 Panduan Penggunaan

### 1. Scanning QR Code/Barcode

#### Camera Scanner
1. Buka tab "Scanner"
2. Arahkan kamera ke QR code atau barcode
3. Aplikasi akan otomatis mendeteksi dan memproses
4. Hasil scan akan ditampilkan dalam modal
5. Pilih aksi yang sesuai (buka URL, salin, share, dll)

#### Gallery Scanner
1. Buka tab "Gallery"
2. Tap "Pilih Gambar" untuk memilih dari gallery
3. Pilih satu atau beberapa gambar yang mengandung QR code
4. Tap "Scan Gambar" untuk memproses
5. Hasil akan ditampilkan setelah processing selesai

### 2. Melihat History Scan
1. Buka tab "History"
2. Scroll untuk melihat semua scan sebelumnya
3. Gunakan search bar untuk mencari scan tertentu
4. Tap item untuk melihat detail lengkap
5. Gunakan filter dan sort untuk mengorganisir data

### 3. Generate QR Code
1. Buka tab "Generate"
2. Pilih tipe QR code yang ingin dibuat:
   - **Text**: Masukkan teks bebas
   - **URL**: Masukkan alamat website
   - **Contact**: Isi informasi kontak lengkap
   - **WiFi**: Masukkan SSID dan password WiFi
3. Tap "Generate QR Code"
4. Preview QR code akan muncul
5. Simpan atau share QR code yang telah dibuat

## 🏗️ Arsitektur Teknis

### Technology Stack
- **Frontend Framework:** React Native dengan Expo
- **Programming Language:** TypeScript
- **Camera Integration:** expo-camera
- **Barcode Scanning:** expo-barcode-scanner
- **Local Database:** expo-sqlite
- **State Management:** React Hooks
- **Navigation:** expo-router

### Struktur Proyek
```
Qr-App/
├── app/                    # Screen utama aplikasi
│   ├── (tabs)/            # Tab navigation screens
│   │   ├── index.tsx      # Scanner screen
│   │   ├── gallery.tsx    # Gallery scanner screen
│   │   ├── explore.tsx    # History screen
│   │   └── generator.tsx  # QR generator screen
│   └── _layout.tsx        # Root layout
├── components/            # Komponen reusable
│   ├── QRScanner.tsx     # Komponen scanner utama
│   ├── GalleryScanner.tsx # Scanner dari gallery
│   ├── ScanResult.tsx    # Display hasil scan
│   ├── ScanHistory.tsx   # Manajemen history
│   ├── QRGenerator.tsx   # Generator QR code
│   ├── ShareModal.tsx    # Modal sharing options
│   └── Settings.tsx      # Pengaturan aplikasi
├── types/                # TypeScript type definitions
│   └── index.ts          # Interface dan enum
├── utils/                # Utility functions
│   ├── database.ts       # Database management
│   ├── qrUtils.ts        # QR processing utilities
│   └── shareUtils.ts     # Sharing dan save utilities
└── assets/               # Asset files (images, sounds, dll)
```

## 📄 License

Distributed under the MIT License.

## 🔄 Changelog

### Version 1.0.0 (Phase 1 MVP)
- ✅ Real-time QR/Barcode scanning
- ✅ Gallery image scanning
- ✅ Scan history management
- ✅ QR code generation
- ✅ Share dan save functionality
- ✅ Material Design 3 UI
- ✅ SQLite database integration
- ✅ Multi-format support
