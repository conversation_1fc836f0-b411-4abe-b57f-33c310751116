/**
 * Komponen Settings untuk konfigurasi aplikasi
 * Mengelola pengaturan user preferences dan app configuration
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
  Linking
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AppSettings } from '../types';
import { databaseManager } from '../utils/database';

interface SettingsProps {
  onSettingsChange?: (settings: AppSettings) => void;
}

/**
 * Komponen untuk mengelola pengaturan aplikasi
 */
export default function Settings({ onSettingsChange }: SettingsProps) {
  const [settings, setSettings] = useState<AppSettings>({
    enableSound: true,
    enableVibration: true,
    enableFlashlight: true,
    autoSave: true,
    historyRetentionDays: 30,
    defaultQRSize: 200,
    theme: 'system'
  });

  const [isLoading, setIsLoading] = useState(true);

  /**
   * Load settings saat komponen dimount
   */
  useEffect(() => {
    loadSettings();
  }, []);

  /**
   * Load settings dari database
   */
  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      const loadedSettings: AppSettings = {
        enableSound: await databaseManager.getSetting('enableSound', true),
        enableVibration: await databaseManager.getSetting('enableVibration', true),
        enableFlashlight: await databaseManager.getSetting('enableFlashlight', true),
        autoSave: await databaseManager.getSetting('autoSave', true),
        historyRetentionDays: await databaseManager.getSetting('historyRetentionDays', 30),
        defaultQRSize: await databaseManager.getSetting('defaultQRSize', 200),
        theme: await databaseManager.getSetting('theme', 'system')
      };

      setSettings(loadedSettings);
      onSettingsChange?.(loadedSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update setting dan simpan ke database
   */
  const updateSetting = async <K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      
      await databaseManager.saveSetting(key, value);
      onSettingsChange?.(newSettings);
    } catch (error) {
      console.error('Error updating setting:', error);
      Alert.alert('Error', 'Gagal menyimpan pengaturan');
    }
  };

  /**
   * Clear history dengan konfirmasi
   */
  const clearHistory = () => {
    Alert.alert(
      'Hapus History',
      'Apakah Anda yakin ingin menghapus semua history scan? Aksi ini tidak dapat dibatalkan.',
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete all non-favorite items
              const cutoffDate = new Date();
              cutoffDate.setFullYear(cutoffDate.getFullYear() + 1); // Far future date to delete all
              await databaseManager.cleanupOldHistory(365 * 10); // 10 years
              Alert.alert('Berhasil', 'History berhasil dihapus');
            } catch (error) {
              console.error('Error clearing history:', error);
              Alert.alert('Error', 'Gagal menghapus history');
            }
          }
        }
      ]
    );
  };

  /**
   * Reset settings ke default
   */
  const resetSettings = () => {
    Alert.alert(
      'Reset Pengaturan',
      'Apakah Anda yakin ingin mereset semua pengaturan ke default?',
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            const defaultSettings: AppSettings = {
              enableSound: true,
              enableVibration: true,
              enableFlashlight: true,
              autoSave: true,
              historyRetentionDays: 30,
              defaultQRSize: 200,
              theme: 'system'
            };

            try {
              for (const [key, value] of Object.entries(defaultSettings)) {
                await databaseManager.saveSetting(key, value);
              }
              setSettings(defaultSettings);
              onSettingsChange?.(defaultSettings);
              Alert.alert('Berhasil', 'Pengaturan berhasil direset');
            } catch (error) {
              console.error('Error resetting settings:', error);
              Alert.alert('Error', 'Gagal mereset pengaturan');
            }
          }
        }
      ]
    );
  };

  /**
   * Open app info/about
   */
  const openAbout = () => {
    Alert.alert(
      'QR Scanner Pro',
      'Versi 1.0.0\n\nAplikasi scanner QR Code dan Barcode dengan fitur lengkap.\n\nDikembangkan dengan React Native dan Expo.',
      [{ text: 'OK' }]
    );
  };

  /**
   * Render setting item dengan switch
   */
  const renderSwitchSetting = (
    title: string,
    subtitle: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    icon: string
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon as any} size={20} color="#007AFF" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingSubtitle}>{subtitle}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#E5E5E7', true: '#007AFF' }}
        thumbColor="#FFFFFF"
      />
    </View>
  );

  /**
   * Render setting item dengan action
   */
  const renderActionSetting = (
    title: string,
    subtitle: string,
    onPress: () => void,
    icon: string,
    showArrow: boolean = true
  ) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon as any} size={20} color="#007AFF" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingSubtitle}>{subtitle}</Text>
      </View>
      {showArrow && (
        <Ionicons name="chevron-forward" size={20} color="#CCCCCC" />
      )}
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Memuat pengaturan...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Scanner Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pengaturan Scanner</Text>
        
        {renderSwitchSetting(
          'Suara Feedback',
          'Mainkan suara saat berhasil scan',
          settings.enableSound,
          (value) => updateSetting('enableSound', value),
          'volume-high'
        )}

        {renderSwitchSetting(
          'Getaran',
          'Berikan feedback getaran saat scan',
          settings.enableVibration,
          (value) => updateSetting('enableVibration', value),
          'phone-portrait'
        )}

        {renderSwitchSetting(
          'Auto Save',
          'Simpan hasil scan otomatis ke history',
          settings.autoSave,
          (value) => updateSetting('autoSave', value),
          'save'
        )}
      </View>

      {/* Data Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Manajemen Data</Text>
        
        {renderActionSetting(
          'Hapus History',
          'Hapus semua history scan (kecuali favorit)',
          clearHistory,
          'trash'
        )}

        {renderActionSetting(
          'Reset Pengaturan',
          'Kembalikan semua pengaturan ke default',
          resetSettings,
          'refresh'
        )}
      </View>

      {/* App Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informasi Aplikasi</Text>
        
        {renderActionSetting(
          'Tentang Aplikasi',
          'Versi dan informasi pengembang',
          openAbout,
          'information-circle'
        )}
      </View>
    </ScrollView>
  );
}

/**
 * Styles untuk komponen Settings
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 20,
    marginHorizontal: 16,
    borderRadius: 12,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
});
