/**
 * Screen untuk menampilkan history scan QR Code
 * Mengintegrasikan komponen ScanHistory dengan modal untuk detail
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  SafeAreaView,
  StatusBar
} from 'react-native';
import ScanHistory from '@/components/ScanHistory';
import ScanResult from '@/components/ScanResult';
import { ScanResult as ScanResultType } from '@/types';
import { databaseManager } from '@/utils/database';

export default function HistoryScreen() {
  const [selectedScanResult, setSelectedScanResult] = useState<ScanResultType | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);

  /**
   * Handle saat item history ditekan
   */
  const handleScanResultPress = (scanResult: ScanResultType) => {
    setSelectedScanResult(scanResult);
    setShowResultModal(true);
  };

  /**
   * Tutup modal detail scan result
   */
  const closeResultModal = () => {
    setShowResultModal(false);
    setSelectedScanResult(null);
  };

  /**
   * Toggle favorite status
   */
  const toggleFavorite = async () => {
    if (!selectedScanResult) return;

    try {
      await databaseManager.toggleFavorite(selectedScanResult.id);
      setSelectedScanResult({
        ...selectedScanResult,
        isFavorite: !selectedScanResult.isFavorite
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* History List */}
      <ScanHistory onScanResultPress={handleScanResultPress} />

      {/* Modal untuk detail scan result */}
      <Modal
        visible={showResultModal}
        animationType="slide"
        transparent={true}
        onRequestClose={closeResultModal}
      >
        {selectedScanResult && (
          <ScanResult
            scanResult={selectedScanResult}
            onClose={closeResultModal}
            onFavorite={toggleFavorite}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
}

/**
 * Styles untuk HistoryScreen
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
